#!/usr/bin/env python3
"""
测试改进版ASB-DPTAM-BiGRU模型
验证残差连接和特征融合的效果
"""

import torch
import torch.nn as nn
import numpy as np
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.models.asb_dptam_bigru_model import ASBDPTAMBiGRUModel
from src.utils.config import ASB_DPTAM_BIGRU_CONFIG

def test_model_architecture():
    """测试改进版模型架构"""
    print("🔧 测试改进版ASB-DPTAM-BiGRU模型架构...")
    
    # 模型参数
    sequence_length = 24
    n_features = 25
    batch_size = 8
    
    # 创建模型
    model = ASBDPTAMBiGRUModel(
        sequence_length=sequence_length,
        n_features=n_features,
        n_segment=6,
        dptam_kernel_size=3,
        bigru_units=[64, 32],
        dense_units=[32, 16],
        dropout_rate=0.3,
        asb_adaptive_filter=True
    )
    
    print(f"\n📊 模型信息:")
    model_info = model.get_model_info()
    for key, value in model_info.items():
        print(f"  {key}: {value}")
    
    # 创建测试数据
    x = torch.randn(batch_size, sequence_length, n_features)
    print(f"\n📥 输入数据形状: {x.shape}")
    
    # 前向传播测试
    print(f"\n🔄 开始前向传播测试...")
    model.eval()
    with torch.no_grad():
        output = model(x)
    
    print(f"✅ 前向传播成功!")
    print(f"📤 输出形状: {output.shape}")
    print(f"📊 输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
    
    return model, output

def test_residual_weights():
    """测试残差权重的可学习性"""
    print(f"\n🎯 测试残差权重...")
    
    model = ASBDPTAMBiGRUModel(
        sequence_length=24,
        n_features=25,
        n_segment=6,
        bigru_units=[32, 16],
        dense_units=[16, 8],
        dropout_rate=0.2
    )
    
    print(f"初始残差权重:")
    print(f"  ASB残差权重: {model.residual_weight_1.item():.4f}")
    print(f"  DPTAM残差权重: {model.residual_weight_2.item():.4f}")
    
    # 检查权重是否可学习
    print(f"\n残差权重可学习性:")
    print(f"  ASB权重requires_grad: {model.residual_weight_1.requires_grad}")
    print(f"  DPTAM权重requires_grad: {model.residual_weight_2.requires_grad}")
    
    return model

def test_feature_fusion():
    """测试特征融合层"""
    print(f"\n🔗 测试特征融合层...")
    
    model = ASBDPTAMBiGRUModel(
        sequence_length=24,
        n_features=25,
        n_segment=6,
        bigru_units=[32, 16],
        dense_units=[16, 8],
        dropout_rate=0.2
    )
    
    # 检查融合层结构
    print(f"ASB融合层结构:")
    for i, layer in enumerate(model.asb_fusion):
        print(f"  层{i}: {layer}")
    
    print(f"\nDPTAM融合层结构:")
    for i, layer in enumerate(model.dptam_fusion):
        print(f"  层{i}: {layer}")
    
    # 测试融合层输入输出
    batch_size, seq_len, n_features = 4, 24, 25
    
    # 模拟ASB融合
    x_asb = torch.randn(batch_size, seq_len, n_features)
    x_original = torch.randn(batch_size, seq_len, n_features)
    x_asb_concat = torch.cat([x_asb, x_original], dim=-1)
    
    print(f"\n特征融合测试:")
    print(f"  ASB特征形状: {x_asb.shape}")
    print(f"  原始特征形状: {x_original.shape}")
    print(f"  拼接后形状: {x_asb_concat.shape}")
    
    with torch.no_grad():
        x_asb_fused = model.asb_fusion(x_asb_concat)
        print(f"  ASB融合后形状: {x_asb_fused.shape}")
    
    return model

def compare_with_original():
    """对比原始架构和改进架构的参数量"""
    print(f"\n📊 对比原始架构和改进架构...")
    
    # 原始架构（模拟）
    original_params = 0
    
    # 改进架构
    improved_model = ASBDPTAMBiGRUModel(
        sequence_length=24,
        n_features=25,
        n_segment=6,
        bigru_units=[64, 32],
        dense_units=[32, 16],
        dropout_rate=0.3
    )
    
    improved_params = sum(p.numel() for p in improved_model.parameters())
    
    # 计算新增参数
    fusion_params = sum(p.numel() for p in improved_model.asb_fusion.parameters())
    fusion_params += sum(p.numel() for p in improved_model.dptam_fusion.parameters())
    residual_params = improved_model.residual_weight_1.numel() + improved_model.residual_weight_2.numel()
    
    print(f"改进架构总参数量: {improved_params:,}")
    print(f"特征融合层参数: {fusion_params:,}")
    print(f"残差权重参数: {residual_params}")
    print(f"新增参数总计: {fusion_params + residual_params:,}")
    
    return improved_model

def main():
    """主测试函数"""
    print("🚀 开始测试改进版ASB-DPTAM-BiGRU模型")
    print("=" * 60)
    
    try:
        # 测试1: 模型架构
        model, output = test_model_architecture()
        
        # 测试2: 残差权重
        test_residual_weights()
        
        # 测试3: 特征融合
        test_feature_fusion()
        
        # 测试4: 参数对比
        compare_with_original()
        
        print(f"\n" + "=" * 60)
        print("✅ 所有测试通过！改进版模型架构验证成功！")
        print(f"\n🎯 改进要点总结:")
        print(f"  ✨ 添加了可学习的残差连接权重")
        print(f"  ✨ 实现了ASB和DPTAM的特征融合层")
        print(f"  ✨ 改善了梯度流和信息传递")
        print(f"  ✨ 保持了原有的串联架构优势")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
