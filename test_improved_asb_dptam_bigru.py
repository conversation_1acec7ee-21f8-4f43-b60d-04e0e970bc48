#!/usr/bin/envpython3"""测试改进版ASB-DPTAM-BiGRU模型验证残差连接和特征融合的效果"""importtorchimporttorch.nnasnnimportnumpyasnpimportsysimportos#添加项目根目录到系统路径sys.path.append(os.path.dirname(os.path.abspath(__file__)))fromsrc.models.asb_dptam_bigru_modelimportASBDPTAMBiGRUModelfromsrc.utils.configimportASB_DPTAM_BIGRU_CONFIGdeftest_model_architecture():"""测试改进版模型架构"""print("测试改进版ASB-DPTAM-BiGRU模型架构...")#模型参数sequence_length=24n_features=25batch_size=8#创建模型model=ASBDPTAMBiGRUModel(sequence_length=sequence_length,n_features=n_features,n_segment=6,dptam_kernel_size=3,bigru_units=[64,32],dense_units=[32,16],dropout_rate=0.3,asb_adaptive_filter=True)print(f"\n模型信息:")model_info=model.get_model_info()forkey,valueinmodel_info.items():print(f"{key}:{value}")#创建测试数据x=torch.randn(batch_size,sequence_length,n_features)print(f"\n📥输入数据形状:{x.shape}")#前向传播测试print(f"\n开始前向传播测试...")model.eval()withtorch.no_grad():output=model(x)print(f"前向传播成功!")print(f"📤输出形状:{output.shape}")print(f"输出范围:[{output.min().item():.4f},{output.max().item():.4f}]")returnmodel,outputdeftest_residual_weights():"""测试残差权重的可学习性"""print(f"\n测试残差权重...")model=ASBDPTAMBiGRUModel(sequence_length=24,n_features=25,n_segment=6,bigru_units=[32,16],dense_units=[16,8],dropout_rate=0.2)print(f"初始残差权重:")print(f"ASB残差权重:{model.residual_weight_1.item():.4f}")print(f"DPTAM残差权重:{model.residual_weight_2.item():.4f}")#检查权重是否可学习print(f"\n残差权重可学习性:")print(f"ASB权重requires_grad:{model.residual_weight_1.requires_grad}")print(f"DPTAM权重requires_grad:{model.residual_weight_2.requires_grad}")returnmodeldeftest_feature_fusion():"""测试特征融合层"""print(f"\n🔗测试特征融合层...")model=ASBDPTAMBiGRUModel(sequence_length=24,n_features=25,n_segment=6,bigru_units=[32,16],dense_units=[16,8],dropout_rate=0.2)#检查融合层结构print(f"ASB融合层结构:")fori,layerinenumerate(model.asb_fusion):print(f"层{i}:{layer}")print(f"\nDPTAM融合层结构:")fori,layerinenumerate(model.dptam_fusion):print(f"层{i}:{layer}")#测试融合层输入输出batch_size,seq_len,n_features=4,24,25#模拟ASB融合x_asb=torch.randn(batch_size,seq_len,n_features)x_original=torch.randn(batch_size,seq_len,n_features)x_asb_concat=torch.cat([x_asb,x_original],dim=-1)print(f"\n特征融合测试:")print(f"ASB特征形状:{x_asb.shape}")print(f"原始特征形状:{x_original.shape}")print(f"拼接后形状:{x_asb_concat.shape}")withtorch.no_grad():x_asb_fused=model.asb_fusion(x_asb_concat)print(f"ASB融合后形状:{x_asb_fused.shape}")returnmodeldefcompare_with_original():"""对比原始架构和改进架构的参数量"""print(f"\n对比原始架构和改进架构...")#原始架构（模拟）original_params=0#改进架构improved_model=ASBDPTAMBiGRUModel(sequence_length=24,n_features=25,n_segment=6,bigru_units=[64,32],dense_units=[32,16],dropout_rate=0.3)improved_params=sum(p.numel()forpinimproved_model.parameters())#计算新增参数fusion_params=sum(p.numel()forpinimproved_model.asb_fusion.parameters())fusion_params+=sum(p.numel()forpinimproved_model.dptam_fusion.parameters())residual_params=improved_model.residual_weight_1.numel()+improved_model.residual_weight_2.numel()print(f"改进架构总参数量:{improved_params:,}")print(f"特征融合层参数:{fusion_params:,}")print(f"残差权重参数:{residual_params}")print(f"新增参数总计:{fusion_params+residual_params:,}")returnimproved_modeldefmain():"""主测试函数"""print("开始测试改进版ASB-DPTAM-BiGRU模型")print("="*60)try:#测试1:模型架构model,output=test_model_architecture()#测试2:残差权重test_residual_weights()#测试3:特征融合test_feature_fusion()#测试4:参数对比compare_with_original()print(f"\n"+"="*60)print("所有测试通过！改进版模型架构验证成功！")print(f"\n改进要点总结:")print(f"添加了可学习的残差连接权重")print(f"实现了ASB和DPTAM的特征融合层")print(f"改善了梯度流和信息传递")print(f"保持了原有的串联架构优势")exceptExceptionase:print(f"\n测试失败:{str(e)}")importtracebacktraceback.print_exc()if__name__=="__main__":main()