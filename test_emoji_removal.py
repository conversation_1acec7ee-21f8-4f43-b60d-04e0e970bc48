#!/usr/bin/env python3
"""
测试emoji删除结果
"""

import os
import re
import sys

def check_emojis_in_file(file_path):
    """检查文件中是否还有emoji"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查常见的emoji
        emoji_patterns = [
            '🎯', '🔧', '📁', '⏰', '📋', '✅', '🔍', '💡', '🚀', '🎉', 
            '✨', '🔬', '⚠️', '❌', '📊', '🏆', '💾', '🔄', '📂', '🎨'
        ]
        
        found_emojis = []
        for emoji in emoji_patterns:
            if emoji in content:
                found_emojis.append(emoji)
        
        return found_emojis
        
    except Exception as e:
        print(f"检查文件 {file_path} 时出错: {e}")
        return []

def main():
    """主函数"""
    print("检查emoji删除结果...")
    print("=" * 50)
    
    # 检查关键文件
    key_files = [
        'src/utils/config.py',
        'src/experiments/asb_dptam_advantage_comparison.py',
        'src/utils/enhanced_training_progress.py',
        'src/utils/visualization_manager.py'
    ]
    
    total_emojis = 0
    
    for file_path in key_files:
        if os.path.exists(file_path):
            emojis = check_emojis_in_file(file_path)
            if emojis:
                print(f"文件 {file_path} 中仍有emoji: {emojis}")
                total_emojis += len(emojis)
            else:
                print(f"文件 {file_path}: 无emoji")
        else:
            print(f"文件不存在: {file_path}")
    
    print("\n" + "=" * 50)
    if total_emojis == 0:
        print("所有关键文件的emoji已成功删除！")
    else:
        print(f"仍有 {total_emojis} 个emoji需要清理")

if __name__ == "__main__":
    main()
