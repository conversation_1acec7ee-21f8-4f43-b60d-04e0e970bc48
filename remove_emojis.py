#!/usr/bin/envpython3"""批量删除代码中的装饰性emoji表情"""importosimportreimportsysdefremove_emojis_from_file(file_path):"""从文件中删除emoji"""try:withopen(file_path,'r',encoding='utf-8')asf:content=f.read()#定义要删除的emoji模式emoji_patterns=[r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'',r'']#删除emoji，但保留空格modified=Falseforpatterninemoji_patterns:ifpatternincontent:#删除emoji但保留后面的空格content=re.sub(pattern+r'\s*','',content)modified=True#如果有修改，写回文件ifmodified:withopen(file_path,'w',encoding='utf-8')asf:f.write(content)print(f"已处理:{file_path}")returnTrueelse:print(f"无需处理:{file_path}")returnFalseexceptExceptionase:print(f"处理文件{file_path}时出错:{e}")returnFalsedefprocess_directory(directory):"""处理目录中的所有Python文件"""processed_count=0forroot,dirs,filesinos.walk(directory):#跳过一些不需要处理的目录dirs[:]=[dfordindirsifnotd.startswith('.')anddnotin['__pycache__','node_modules']]forfileinfiles:iffile.endswith('.py'):file_path=os.path.join(root,file)ifremove_emojis_from_file(file_path):processed_count+=1returnprocessed_countdefmain():"""主函数"""print("开始删除代码中的装饰性emoji...")print("="*50)#处理主要目录directories_to_process=['src','modules','fenjie','pedict']total_processed=0fordirectoryindirectories_to_process:ifos.path.exists(directory):print(f"\n处理目录:{directory}")count=process_directory(directory)total_processed+=countprint(f"该目录处理了{count}个文件")else:print(f"目录不存在:{directory}")#处理根目录的Python文件print(f"\n处理根目录文件...")root_files=[fforfinos.listdir('.')iff.endswith('.py')]forfileinroot_files:ifremove_emojis_from_file(file):total_processed+=1print("\n"+"="*50)print(f"emoji删除完成！总共处理了{total_processed}个文件")if__name__=="__main__":main()