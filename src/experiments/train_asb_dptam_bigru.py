"""ASB-DPTAM-BiGRU融合模型训练脚本结合自适应频谱块(ASB)、时序注意力机制(DPTAM)和双向GRU的风电功率预测模型"""importosimportsysimporttorchimportnumpyasnpimportmatplotlib.pyplotaspltfromdatetimeimportdatetime#添加项目根目录到系统路径sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))fromsrc.data_processing.data_loaderimportDataLoaderfromsrc.data_processing.preprocessorimportDataPreprocessorfromsrc.models.asb_dptam_bigru_modelimportASBDPTAMBiGRUModel,ASB_DPTAM_BIGRU_CONFIGfromsrc.utils.visualization_managerimportauto_visualizefromsrc.utils.metricsimportModelEvaluatorfromsrc.utils.configimport(MODEL_CONFIG,DATASET_CONFIG,setup_matplotlib,setup_training_session,get_current_paths)classASBDPTAMBiGRUExperiment:"""ASB-DPTAM-BiGRU融合模型实验类"""def__init__(self):"""初始化实验"""self.data_loader=Noneself.preprocessor=Noneself.model=Noneself.history=Noneself.results=None#设置matplotlibsetup_matplotlib()#设置训练会话setup_training_session('ASB_DPTAM_BiGRU_Experiment')print("="*80)print("ASB-DPTAM-BiGRU融合模型风电功率预测实验")print("="*80)print("模型特点:")print("•ASB(自适应频谱块):频域特征增强，自适应噪声滤波")print("•DPTAM(时序注意力):分段时序注意力机制")print("•BiGRU(双向GRU):双向时序特征提取")print("•融合策略:ASB+DPTAM特征融合→BiGRU处理")print("="*80)defload_and_preprocess_data(self)->dict:"""加载和预处理数据Returns:处理后的数据字典"""print("\n"+"="*60)print("步骤1:数据加载与预处理")print("="*60)#数据加载self.data_loader=DataLoader()raw_data=self.data_loader.load_data()print(f"原始数据形状:{raw_data.shape}")#显示原始数据的Power统计信息print(f"原始Power数据统计:")print(f"最小值:{raw_data[DATASET_CONFIG['target_column']].min():.4f}")print(f"最大值:{raw_data[DATASET_CONFIG['target_column']].max():.4f}")print(f"平均值:{raw_data[DATASET_CONFIG['target_column']].mean():.4f}")print(f"标准差:{raw_data[DATASET_CONFIG['target_column']].std():.4f}")#数据分析（跳过可视化以避免阻塞）self.data_loader.analyze_data()#self.data_loader.visualize_data(save_path=get_current_paths()['figures'])#跳过可视化#数据预处理self.preprocessor=DataPreprocessor()self.preprocessor.set_data(raw_data)df_processed=self.preprocessor.preprocess()feature_columns=self.preprocessor.select_features()#保存预处理后的数据self.preprocessor.save_processed_data()#准备训练数据processed_data=self.preprocessor.prepare_data_for_training(sequence_length=MODEL_CONFIG['sequence_length'],train_ratio=MODEL_CONFIG['train_ratio'],val_ratio=MODEL_CONFIG['val_ratio'])#创建PyTorch数据加载器train_loader,val_loader,test_loader=self.preprocessor.create_pytorch_dataloaders(processed_data,batch_size=MODEL_CONFIG['batch_size'])#将数据加载器添加到数据字典中processed_data['train_loader']=train_loaderprocessed_data['val_loader']=val_loaderprocessed_data['test_loader']=test_loaderprint(f"数据准备完成，特征数量:{processed_data['n_features']}")print(f"使用的标准化器类型:{type(self.preprocessor.scaler_y).__name__}")print(f"序列长度:{processed_data['sequence_length']}")print(f"训练集大小:{len(processed_data['train_loader'].dataset)}")print(f"验证集大小:{len(processed_data['val_loader'].dataset)}")print(f"测试集大小:{len(processed_data['test_loader'].dataset)}")returnprocessed_datadeftrain_asb_dptam_bigru_model(self,data:dict)->None:"""训练ASB-DPTAM-BiGRU融合模型Args:data:训练数据"""print("\n"+"="*60)print("步骤2:训练ASB-DPTAM-BiGRU融合模型")print("="*60)#创建ASB-DPTAM-BiGRU融合模型self.model=ASBDPTAMBiGRUModel(sequence_length=data['sequence_length'],n_features=data['n_features'],n_segment=ASB_DPTAM_BIGRU_CONFIG['n_segment'],dptam_kernel_size=ASB_DPTAM_BIGRU_CONFIG['dptam_kernel_size'],bigru_units=ASB_DPTAM_BIGRU_CONFIG['bigru_units'],dense_units=ASB_DPTAM_BIGRU_CONFIG['dense_units'],dropout_rate=ASB_DPTAM_BIGRU_CONFIG['dropout_rate'],asb_adaptive_filter=ASB_DPTAM_BIGRU_CONFIG['asb_adaptive_filter'])#设置标准化器self.model.set_scalers(self.preprocessor.scaler_X,self.preprocessor.scaler_y)#训练模型self.history=self.model.train_model(train_loader=data['train_loader'],val_loader=data['val_loader'],epochs=MODEL_CONFIG['epochs'],patience=MODEL_CONFIG['patience'],learning_rate=MODEL_CONFIG['learning_rate'])#保存模型self.model.save_model()#评估模型self.results=self.model.evaluate(X_train=data['X_train'],y_train=data['y_train'],X_val=data['X_val'],y_val=data['y_val'],X_test=data['X_test'],y_test=data['y_test'],use_normalized_metrics=True)print("ASB-DPTAM-BiGRU融合模型训练完成！")defanalyze_and_visualize(self,data:dict)->None:"""分析和可视化结果Args:data:数据字典"""print("\n"+"="*60)print("步骤3:结果分析与可视化")print("="*60)#获取模型信息model_info=self.model.get_model_info()#自动可视化auto_visualize(model=self.model,history=self.history,results=self.results,model_info=model_info,data=data,experiment_name="ASB_DPTAM_BiGRU_Experiment")#打印详细结果self.print_detailed_results(model_info)defprint_detailed_results(self,model_info:dict)->None:"""打印详细的实验结果"""print("\n"+"="*80)print("ASB-DPTAM-BiGRU融合模型实验结果报告")print("="*80)#模型架构信息print(f"\n模型架构信息:")print(f"模型名称:{model_info['model_name']}")print(f"融合策略:{model_info['fusion_strategy']}")print(f"序列长度:{model_info['sequence_length']}")print(f"特征数量:{model_info['n_features']}")#ASB配置print(f"\nASB(自适应频谱块)配置:")print(f"自适应滤波:{model_info['asb_adaptive_filter']}")print(f"功能:频域特征增强，自适应噪声滤波")#DPTAM配置print(f"\nDPTAM(时序注意力)配置:")print(f"分段数:{model_info['n_segment']}")print(f"每段长度:{model_info['segment_length']}")print(f"卷积核大小:{model_info['dptam_kernel_size']}")print(f"功能:分段时序注意力权重生成")#BiGRU配置print(f"\nBiGRU(双向GRU)配置:")print(f"GRU层单元数:{model_info['bigru_units']}")print(f"全连接层单元数:{model_info['dense_units']}")print(f"双向处理:{model_info['bidirectional']}")print(f"Dropout率:{model_info['dropout_rate']}")print(f"功能:双向时序特征提取")#性能指标print(f"\n模型性能指标:")fordatasetin['train','val','test']:ifdatasetinself.results:metrics=self.results[dataset]print(f"{dataset.upper()}集:")print(f"MAE:{metrics['mae']:.4f}")print(f"MSE:{metrics['mse']:.4f}")print(f"RMSE:{metrics['rmse']:.4f}")print(f"MAPE:{metrics['mape']:.2f}%")print(f"R²:{metrics['r2']:.4f}")#训练信息ifself.history:print(f"\n训练信息:")print(f"最终训练损失:{self.history['train_loss'][-1]:.6f}")print(f"最终验证损失:{self.history['val_loss'][-1]:.6f}")print(f"训练轮数:{len(self.history['train_loss'])}")#找到最佳验证损失best_val_loss=min(self.history['val_loss'])best_epoch=self.history['val_loss'].index(best_val_loss)+1print(f"最佳验证损失:{best_val_loss:.6f}(第{best_epoch}轮)")print("\n"+"="*80)defrun_experiment(self)->None:"""运行完整实验"""try:#步骤1:数据加载与预处理data=self.load_and_preprocess_data()#步骤2:模型训练self.train_asb_dptam_bigru_model(data)#步骤3:结果分析与可视化self.analyze_and_visualize(data)print(f"\nASB-DPTAM-BiGRU融合模型实验完成！")print(f"结果保存路径:{get_current_paths()['results_root']}")exceptExceptionase:print(f"\n实验过程中发生错误:{str(e)}")importtracebacktraceback.print_exc()raisedefmain():"""主函数"""#创建并运行实验experiment=ASBDPTAMBiGRUExperiment()experiment.run_experiment()if__name__=="__main__":main()