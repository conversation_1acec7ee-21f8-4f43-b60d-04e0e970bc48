"""内存优化的DPTAM-BiGRU融合模型训练脚本针对GPU内存有限的情况进行优化"""importosimportsysimportnumpyasnpimportpandasaspdimporttorchimportwarningsimportgcwarnings.filterwarnings('ignore')#设置随机种子torch.manual_seed(42)np.random.seed(42)#添加项目根目录到系统路径sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))fromsrc.data_processing.data_loaderimportDataLoaderfromsrc.data_processing.preprocessorimportDataPreprocessorfromsrc.models.dptam_bigru_modelimportDPTAMBiGRUModelfromsrc.utils.visualizationimportAcademicVisualizerfromsrc.utils.metricsimportModelEvaluatorfromsrc.utils.configimportDATA_PATHS,MODEL_CONFIG,DPTAM_BIGRU_CONFIG,setup_matplotlibclassMemoryOptimizedDPTAMBiGRUTrainer:"""内存优化的DPTAM-BiGRU融合模型训练器"""def__init__(self):"""初始化训练器"""self.data_loader=DataLoader()self.preprocessor=DataPreprocessor()self.visualizer=AcademicVisualizer()self.evaluator=ModelEvaluator()self.model=Noneself.history=Noneself.results=None#设置matplotlibsetup_matplotlib()#内存优化设置self.setup_memory_optimization()print("内存优化的DPTAM-BiGRU融合模型训练器初始化完成")print(f"当前配置-批次大小:{MODEL_CONFIG['batch_size']}")print(f"BiGRU单元数:{DPTAM_BIGRU_CONFIG['bigru_units']}")print(f"全连接层:{DPTAM_BIGRU_CONFIG['dense_units']}")defsetup_memory_optimization(self):"""设置内存优化"""#清理GPU缓存iftorch.cuda.is_available():torch.cuda.empty_cache()print(f"GPU内存清理完成")print(f"可用GPU内存:{torch.cuda.get_device_properties(0).total_memory/1024**3:.1f}GB")#设置内存增长策略torch.backends.cudnn.benchmark=Falsetorch.backends.cudnn.deterministic=True#启用内存映射torch.multiprocessing.set_sharing_strategy('file_system')defload_and_prepare_data(self)->dict:"""加载和准备数据（内存优化版本）Returns:准备好的训练数据"""print("\n"+"="*60)print("步骤1:数据加载和预处理（内存优化）")print("="*60)#加载数据df=self.data_loader.load_data()#显示原始数据的Power统计信息print(f"原始Power数据统计:")print(f"最小值:{df['Power'].min():.4f}")print(f"最大值:{df['Power'].max():.4f}")print(f"平均值:{df['Power'].mean():.4f}")print(f"标准差:{df['Power'].std():.4f}")#数据预处理self.preprocessor.set_data(df)df_processed=self.preprocessor.preprocess()feature_columns=self.preprocessor.select_features()#保存预处理后的数据self.preprocessor.save_processed_data()#准备训练数据data=self.preprocessor.prepare_data_for_training(sequence_length=MODEL_CONFIG['sequence_length'],train_ratio=MODEL_CONFIG['train_ratio'],val_ratio=MODEL_CONFIG['val_ratio'])#创建PyTorch数据加载器（使用较小的批次大小）train_loader,val_loader,test_loader=self.preprocessor.create_pytorch_dataloaders(data,batch_size=MODEL_CONFIG['batch_size']#已经在config中减少到8)#将数据加载器添加到数据字典中data['train_loader']=train_loaderdata['val_loader']=val_loaderdata['test_loader']=test_loaderprint(f"数据准备完成，特征数量:{data['n_features']}")print(f"训练批次大小:{MODEL_CONFIG['batch_size']}")#清理内存deldf,df_processedgc.collect()iftorch.cuda.is_available():torch.cuda.empty_cache()returndatadeftrain_dptam_bigru_model(self,data:dict)->None:"""训练DPTAM-BiGRU融合模型（内存优化版本）Args:data:训练数据"""print("\n"+"="*60)print("步骤2:训练DPTAM-BiGRU融合模型（内存优化）")print("="*60)#创建DPTAM-BiGRU融合模型（使用config.py中的配置）self.model=DPTAMBiGRUModel(sequence_length=data['sequence_length'],n_features=data['n_features'],n_segment=DPTAM_BIGRU_CONFIG['n_segment'],dptam_kernel_size=DPTAM_BIGRU_CONFIG['dptam_kernel_size'],bigru_units=DPTAM_BIGRU_CONFIG['bigru_units'],dense_units=DPTAM_BIGRU_CONFIG['dense_units'],dropout_rate=DPTAM_BIGRU_CONFIG['dropout_rate'])#显示模型信息model_info=self.model.get_model_info()print(f"模型参数量:{model_info['total_params']:,}")#设置标准化器self.model.set_scalers(self.preprocessor.scaler_X,self.preprocessor.scaler_y)#训练模型（使用梯度累积来模拟更大的批次）self.history=self.model.train_model(train_loader=data['train_loader'],val_loader=data['val_loader'],epochs=MODEL_CONFIG['epochs'],patience=MODEL_CONFIG['patience'],learning_rate=MODEL_CONFIG['learning_rate'])#保存模型self.model.save_model()#评估模型（使用标准化后的指标）print("开始模型评估...")self.results=self.model.evaluate(X_train=data['X_train'],y_train=data['y_train'],X_val=data['X_val'],y_val=data['y_val'],X_test=data['X_test'],y_test=data['y_test'],use_normalized_metrics=True)print("DPTAM-BiGRU融合模型训练完成！")defvisualize_results(self)->None:"""可视化训练结果"""print("\n"+"="*60)print("步骤3:结果可视化")print("="*60)ifself.modelisNoneorself.historyisNone:print("模型未训练，跳过可视化")return#可视化训练历史self.visualizer.plot_training_history(self.history,title=f'{self.model.model_name}模型训练历史（内存优化版）',save_path=os.path.join(DATA_PATHS['figures'],f'{self.model.model_name.lower()}_memory_opt_training_history.png'))#可视化预测结果ifself.results:self.visualizer.plot_predictions(self.results,title=f'{self.model.model_name}模型预测结果（内存优化版）',save_path=os.path.join(DATA_PATHS['figures'],f'{self.model.model_name.lower()}_memory_opt_predictions.png'))print("结果可视化完成")defprint_detailed_results(self)->None:"""打印详细的训练结果"""print("\n"+"="*60)print("步骤4:详细结果报告（内存优化版）")print("="*60)ifself.resultsisNone:print("没有可用的结果数据")return#打印模型信息model_info=self.model.get_model_info()print("模型配置信息（内存优化版）:")print(f"模型名称:{model_info['model_name']}")print(f"总参数量:{model_info['total_params']:,}")print(f"批次大小:{MODEL_CONFIG['batch_size']}")print(f"BiGRU单元数:{model_info['bigru_units']}")print(f"全连接层:{model_info['dense_units']}")print(f"\n性能指标(标准化数据):")#测试集结果print(f"\n测试集:")formetric,valueinself.results['test'].items():print(f"{metric}:{value:.6f}")#训练信息ifself.history:print(f"\n训练信息:")print(f"训练轮数:{len(self.history['train_loss'])}")print(f"最佳验证损失:{min(self.history['val_loss']):.6f}")print(f"最终训练损失:{self.history['train_loss'][-1]:.6f}")print(f"最终验证损失:{self.history['val_loss'][-1]:.6f}")defrun_experiment(self)->None:"""运行完整的实验流程"""print("开始内存优化的DPTAM-BiGRU融合模型风电功率预测实验")print("针对GPU内存有限的情况进行优化")print("="*60)try:#1.数据准备data=self.load_and_prepare_data()#2.训练DPTAM-BiGRU融合模型self.train_dptam_bigru_model(data)#3.可视化结果self.visualize_results()#4.打印详细结果self.print_detailed_results()#找到最佳指标test_rmse=self.results['test']['RMSE']print("\n内存优化的DPTAM-BiGRU融合模型实验成功完成！")print(f"测试集RMSE:{test_rmse:.6f}")print(f"模型总参数量:{self.model.get_model_info()['total_params']:,}")print(f"使用批次大小:{MODEL_CONFIG['batch_size']}")exceptExceptionase:print(f"\n实验过程中出现错误:{str(e)}")importtracebacktraceback.print_exc()finally:#清理内存iftorch.cuda.is_available():torch.cuda.empty_cache()gc.collect()#主程序if__name__=="__main__":trainer=MemoryOptimizedDPTAMBiGRUTrainer()trainer.run_experiment()