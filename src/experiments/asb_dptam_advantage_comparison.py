"""
ASB-DPTAM优势对比分析实验
对比分析基线BiGRU、DPTAM-BiGRU和的性能差异
验证ASB频域增强和DPTAM时序注意力机制的有效性
"""

import os
import sys

# 设置环境变量以避免OpenMP冲突
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.models.bigru_model import BiGRUModel
from src.models.dptam_bigru_model import DPTAMBiGRUModel
from src.models.asb_dptam_bigru_model import ASBDPTAMBiGRUModel
from src.models.bilstm_model import BiLSTMModel
from src.models.cnn_bigru_model import CNNBiGRUModel
from src.utils.visualization_manager import auto_visualize
from src.utils.metrics import ModelEvaluator
from src.utils.config import (
    MODEL_CONFIG, DATASET_CONFIG, BIGRU_CONFIG, DPTAM_BIGRU_CONFIG, ASB_DPTAM_BIGRU_CONFIG,
    BILSTM_CONFIG, CNN_BIGRU_CONFIG, COMPARISON_EXPERIMENT_CONFIG,
    setup_matplotlib, setup_training_session, get_current_paths
)


class ASBDPTAMAdvantageComparator:
    """ASB-DPTAM优势对比分析实验类"""

    def __init__(self):
        """初始化对比实验"""
        self.models = {}
        self.results = {}
        self.histories = {}
        self.data_loader = None
        self.preprocessor = None

        # 设置matplotlib和训练会话
        setup_matplotlib()
        print("🚀 正在设置训练会话...")
        training_paths = setup_training_session()  # 使用时间戳格式

        # 验证训练会话设置
        print("🔍 验证训练会话设置:")
        for key, path in training_paths.items():
            exists = os.path.exists(path) if isinstance(path, str) else "N/A"
            print(f"   {key}: {path} (存在: {exists})")

        print("=" * 80)
        print("ASB-DPTAM优势对比分析实验")
        print("=" * 80)
        print("实验目标:")
        print("• 验证ASB频域增强的有效性")
        print("• 验证DPTAM双路径时间注意力的有效性")
        print("• 对比不同模型架构的性能")
        print("• 分析各组件的贡献度")
        print("=" * 80)

    def safe_save_model(self, model, filename: str) -> str:
        """
        安全保存模型，确保目录存在

        Args:
            model: 要保存的模型
            filename: 文件名

        Returns:
            完整的保存路径
        """
        print(f"🔍 开始安全保存模型: {filename}")

        # 获取当前路径并打印调试信息
        current_paths = get_current_paths()
        print(f"📁 当前路径信息:")
        for key, path in current_paths.items():
            print(f"   {key}: {path}")

        model_path = os.path.join(current_paths['models'], filename)
        model_dir = os.path.dirname(model_path)

        print(f"🎯 目标模型路径: {model_path}")
        print(f"📂 目标模型目录: {model_dir}")

        # 检查并创建完整的目录路径
        print(f"🔍 检查目录结构...")

        # 分解路径，逐级创建目录
        path_parts = []
        temp_path = model_dir
        while temp_path and temp_path != os.path.dirname(temp_path):
            path_parts.append(temp_path)
            temp_path = os.path.dirname(temp_path)
        path_parts.reverse()

        print(f"📋 需要创建的目录层级:")
        for i, part in enumerate(path_parts):
            print(f"   {i+1}. {part}")

        # 逐级创建目录
        for i, directory in enumerate(path_parts):
            if not os.path.exists(directory):
                print(f"🔧 创建目录 {i+1}/{len(path_parts)}: {directory}")
                try:
                    os.makedirs(directory, exist_ok=True)
                    if os.path.exists(directory):
                        print(f"✅ 成功创建: {directory}")
                    else:
                        print(f"❌ 创建失败但无异常: {directory}")
                        raise RuntimeError(f"目录创建失败: {directory}")
                except Exception as e:
                    print(f"❌ 创建目录失败: {directory}")
                    print(f"   错误信息: {e}")
                    print(f"   当前工作目录: {os.getcwd()}")
                    print(f"   目录权限检查:")
                    parent = os.path.dirname(directory)
                    if os.path.exists(parent):
                        print(f"     父目录存在: {parent}")
                        print(f"     父目录可写: {os.access(parent, os.W_OK)}")
                    else:
                        print(f"     父目录不存在: {parent}")
                    raise
            else:
                print(f"✅ 目录已存在 {i+1}/{len(path_parts)}: {directory}")

        # 最终验证
        if not os.path.exists(model_dir):
            raise RuntimeError(f"模型目录创建失败: {model_dir}")

        print(f"🎉 所有目录创建完成，最终目录: {model_dir}")
        print(f"📊 目录状态检查:")
        print(f"   存在: {os.path.exists(model_dir)}")
        print(f"   可写: {os.access(model_dir, os.W_OK)}")
        print(f"   是目录: {os.path.isdir(model_dir)}")

        # 保存模型 - 改进的错误处理
        print(f"💾 开始保存模型到: {model_path}")
        try:
            # 首先将模型移到CPU以避免CUDA问题
            if hasattr(model, 'cpu'):
                print("🔄 将模型移至CPU以避免CUDA问题...")
                model_cpu = model.cpu()
            else:
                model_cpu = model

            # 使用改进的保存方法
            if hasattr(model_cpu, 'save_model'):
                # 使用模型自定义的保存方法
                model_cpu.save_model(model_path)
            else:
                # 使用PyTorch标准保存方法
                import torch
                torch.save(model_cpu.state_dict(), model_path)
                print(f"使用PyTorch标准方法保存模型")

            # 验证文件是否成功保存
            if os.path.exists(model_path):
                file_size = os.path.getsize(model_path)
                print(f"✅ 模型保存成功: {model_path}")
                print(f"   文件大小: {file_size:,} bytes")
            else:
                raise RuntimeError(f"模型文件未成功创建: {model_path}")

            # 将模型移回原设备
            if hasattr(model, 'to') and hasattr(model, 'device'):
                model.to(model.device)
                print(f"🔄 模型已移回原设备: {model.device}")

            return model_path

        except Exception as e:
            print(f"❌ 保存模型失败: {e}")
            print(f"   模型路径: {model_path}")
            print(f"   目录存在: {os.path.exists(model_dir)}")
            print(f"   目录可写: {os.access(model_dir, os.W_OK) if os.path.exists(model_dir) else 'N/A'}")
            print(f"   错误类型: {type(e).__name__}")

            # 尝试备用保存方法
            try:
                print("🔄 尝试备用保存方法...")
                backup_path = model_path.replace('.pth', '_backup.pth')

                # 确保模型在CPU上
                if hasattr(model, 'cpu'):
                    model_cpu = model.cpu()
                else:
                    model_cpu = model

                # 使用最基本的torch.save
                import torch
                torch.save(model_cpu.state_dict(), backup_path)

                if os.path.exists(backup_path):
                    print(f"✅ 备用保存成功: {backup_path}")
                    return backup_path
                else:
                    raise RuntimeError("备用保存也失败")

            except Exception as backup_error:
                print(f"❌ 备用保存也失败: {backup_error}")
                raise e  # 抛出原始错误

        # 显示启用的模型
        enabled_models = []
        if COMPARISON_EXPERIMENT_CONFIG['enable_baseline_bigru']:
            enabled_models.append("Baseline-BiGRU")
        if COMPARISON_EXPERIMENT_CONFIG['enable_dptam_bigru']:
            enabled_models.append("DPTAM-BiGRU")
        if COMPARISON_EXPERIMENT_CONFIG['enable_asb_dptam_bigru']:
            enabled_models.append("ASB-DPTAM-BiGRU")
        if COMPARISON_EXPERIMENT_CONFIG['enable_bilstm']:
            enabled_models.append("BiLSTM")
        if COMPARISON_EXPERIMENT_CONFIG['enable_cnn_bigru']:
            enabled_models.append("CNN-BiGRU")

        print(f"启用的模型: {', '.join(enabled_models)}")
        print("=" * 80)

    def load_and_prepare_data(self) -> dict:
        """加载和预处理数据"""
        print("\n步骤1: 数据加载与预处理")
        print("-" * 40)

        # 数据加载
        self.data_loader = DataLoader()
        raw_data = self.data_loader.load_data()

        print(f"原始数据形状: {raw_data.shape}")

        # 显示原始数据的Power统计信息
        print(f"原始Power数据统计:")
        print(f"  最小值: {raw_data[DATASET_CONFIG['target_column']].min():.4f}")
        print(f"  最大值: {raw_data[DATASET_CONFIG['target_column']].max():.4f}")
        print(f"  平均值: {raw_data[DATASET_CONFIG['target_column']].mean():.4f}")
        print(f"  标准差: {raw_data[DATASET_CONFIG['target_column']].std():.4f}")

        # 数据分析（跳过可视化以避免阻塞）
        self.data_loader.analyze_data()

        # 数据预处理
        self.preprocessor = DataPreprocessor()
        self.preprocessor.set_data(raw_data)
        df_processed = self.preprocessor.preprocess()
        feature_columns = self.preprocessor.select_features()

        # 保存预处理后的数据
        self.preprocessor.save_processed_data()

        # 准备训练数据
        processed_data = self.preprocessor.prepare_data_for_training(
            sequence_length=MODEL_CONFIG['sequence_length'],
            train_ratio=MODEL_CONFIG['train_ratio'],
            val_ratio=MODEL_CONFIG['val_ratio']
        )

        # 创建PyTorch数据加载器
        train_loader, val_loader, test_loader = self.preprocessor.create_pytorch_dataloaders(
            processed_data,
            batch_size=MODEL_CONFIG['batch_size']
        )

        # 将数据加载器添加到数据字典中
        processed_data['train_loader'] = train_loader
        processed_data['val_loader'] = val_loader
        processed_data['test_loader'] = test_loader

        # 保存测试数据加载器为类属性，用于后续可视化
        self.test_loader = test_loader

        print(f"数据准备完成，特征数量: {processed_data['n_features']}")
        print(f"使用的标准化器类型: {type(self.preprocessor.scaler_y).__name__}")
        print(f"序列长度: {processed_data['sequence_length']}")
        print(f"训练集大小: {len(processed_data['train_loader'].dataset)}")
        print(f"验证集大小: {len(processed_data['val_loader'].dataset)}")
        print(f"测试集大小: {len(processed_data['test_loader'].dataset)}")

        return processed_data

    def train_baseline_bigru(self, data: dict) -> None:
        """训练基线BiGRU模型"""
        print("\n步骤2: 训练基线BiGRU模型")
        print("-" * 40)

        # 创建基线BiGRU模型
        self.models['Baseline-BiGRU'] = BiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            bigru_units=BIGRU_CONFIG['bigru_units'],
            dense_units=BIGRU_CONFIG['dense_units'],
            dropout_rate=BIGRU_CONFIG['dropout_rate']
        )

        # 设置标准化器
        self.models['Baseline-BiGRU'].set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )

        # 训练模型
        self.histories['Baseline-BiGRU'] = self.models['Baseline-BiGRU'].train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )

        # 评估模型
        self.results['Baseline-BiGRU'] = self.models['Baseline-BiGRU'].evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )

        # 保存基线BiGRU模型
        baseline_model_path = self.safe_save_model(self.models['Baseline-BiGRU'], 'baseline_bigru_model.pth')

        baseline_info = self.models['Baseline-BiGRU'].get_model_info()
        print(f"✅ 基线BiGRU训练完成")
        print(f"   参数量: {baseline_info['total_params']:,}")
        print(f"   测试RMSE: {self.results['Baseline-BiGRU']['test']['RMSE']:.6f}")
        print(f"   模型已保存: {baseline_model_path}")

    def train_dptam_bigru(self, data: dict) -> None:
        """训练DPTAM-BiGRU模型"""
        print("\n步骤3: 训练DPTAM-BiGRU模型")
        print("-" * 40)

        # 创建DPTAM-BiGRU模型
        self.models['DPTAM-BiGRU'] = DPTAMBiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            n_segment=DPTAM_BIGRU_CONFIG['n_segment'],
            dptam_kernel_size=DPTAM_BIGRU_CONFIG['dptam_kernel_size'],
            bigru_units=DPTAM_BIGRU_CONFIG['bigru_units'],
            dense_units=DPTAM_BIGRU_CONFIG['dense_units'],
            dropout_rate=DPTAM_BIGRU_CONFIG['dropout_rate']
        )

        # 设置标准化器
        self.models['DPTAM-BiGRU'].set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )

        # 训练模型
        self.histories['DPTAM-BiGRU'] = self.models['DPTAM-BiGRU'].train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )

        # 评估模型
        self.results['DPTAM-BiGRU'] = self.models['DPTAM-BiGRU'].evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )

        # 保存DPTAM-BiGRU模型
        dptam_model_path = self.safe_save_model(self.models['DPTAM-BiGRU'], 'dptam_bigru_model.pth')

        dptam_info = self.models['DPTAM-BiGRU'].get_model_info()
        print(f"✅ DPTAM-BiGRU训练完成")
        print(f"   参数量: {dptam_info['total_params']:,}")
        print(f"   测试RMSE: {self.results['DPTAM-BiGRU']['test']['RMSE']:.6f}")
        print(f"   模型已保存: {dptam_model_path}")

    def train_asb_dptam_bigru(self, data: dict) -> None:
        """训练ASB-DPTAM-BiGRU模型"""
        print("\n步骤4: 训练ASB-DPTAM-BiGRU模型")
        print("-" * 40)

        # 创建ASB-DPTAM-BiGRU模型
        self.models['ASB-DPTAM-BiGRU'] = ASBDPTAMBiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            n_segment=ASB_DPTAM_BIGRU_CONFIG['n_segment'],
            dptam_kernel_size=ASB_DPTAM_BIGRU_CONFIG['dptam_kernel_size'],
            bigru_units=ASB_DPTAM_BIGRU_CONFIG['bigru_units'],
            dense_units=ASB_DPTAM_BIGRU_CONFIG['dense_units'],
            dropout_rate=ASB_DPTAM_BIGRU_CONFIG['dropout_rate'],
            asb_adaptive_filter=ASB_DPTAM_BIGRU_CONFIG['asb_adaptive_filter']
        )

        # 设置标准化器
        self.models['ASB-DPTAM-BiGRU'].set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )

        # 训练模型
        self.histories['ASB-DPTAM-BiGRU'] = self.models['ASB-DPTAM-BiGRU'].train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )

        # 评估模型
        self.results['ASB-DPTAM-BiGRU'] = self.models['ASB-DPTAM-BiGRU'].evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )

        # 保存ASB-DPTAM-BiGRU模型
        asb_model_path = self.safe_save_model(self.models['ASB-DPTAM-BiGRU'], 'asb_dptam_bigru_model.pth')

        asb_info = self.models['ASB-DPTAM-BiGRU'].get_model_info()
        print(f"✅ ASB-DPTAM-BiGRU训练完成")
        print(f"   参数量: {asb_info['total_params']:,}")
        print(f"   测试RMSE: {self.results['ASB-DPTAM-BiGRU']['test']['RMSE']:.6f}")
        print(f"   模型已保存: {asb_model_path}")

    def train_bilstm(self, data: dict) -> None:
        """训练BiLSTM模型"""
        print("\n步骤5: 训练BiLSTM模型")
        print("-" * 40)

        # 创建BiLSTM模型
        self.models['BiLSTM'] = BiLSTMModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            bilstm_units=BILSTM_CONFIG['bilstm_units'],
            dense_units=BILSTM_CONFIG['dense_units'],
            dropout_rate=BILSTM_CONFIG['dropout_rate']
        )

        # 设置标准化器
        self.models['BiLSTM'].set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )

        # 训练模型
        self.histories['BiLSTM'] = self.models['BiLSTM'].train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )

        # 评估模型
        self.results['BiLSTM'] = self.models['BiLSTM'].evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )

        # 保存BiLSTM模型
        bilstm_model_path = self.safe_save_model(self.models['BiLSTM'], 'bilstm_model.pth')

        bilstm_info = self.models['BiLSTM'].get_model_info()
        print(f"✅ BiLSTM训练完成")
        print(f"   参数量: {bilstm_info['total_params']:,}")
        print(f"   测试RMSE: {self.results['BiLSTM']['test']['RMSE']:.6f}")
        print(f"   模型已保存: {bilstm_model_path}")

    def train_cnn_bigru(self, data: dict) -> None:
        """训练CNN-BiGRU模型"""
        print("\n步骤6: 训练CNN-BiGRU模型")
        print("-" * 40)

        # 创建CNN-BiGRU模型
        self.models['CNN-BiGRU'] = CNNBiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            cnn_filters=CNN_BIGRU_CONFIG['cnn_filters'],
            cnn_kernel_sizes=CNN_BIGRU_CONFIG['cnn_kernel_sizes'],
            bigru_units=CNN_BIGRU_CONFIG['bigru_units'],
            dense_units=CNN_BIGRU_CONFIG['dense_units'],
            dropout_rate=CNN_BIGRU_CONFIG['dropout_rate'],
            pool_size=CNN_BIGRU_CONFIG['pool_size']
        )

        # 设置标准化器
        self.models['CNN-BiGRU'].set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )

        # 训练模型
        self.histories['CNN-BiGRU'] = self.models['CNN-BiGRU'].train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )

        # 评估模型
        self.results['CNN-BiGRU'] = self.models['CNN-BiGRU'].evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )

        # 保存CNN-BiGRU模型
        cnn_bigru_model_path = self.safe_save_model(self.models['CNN-BiGRU'], 'cnn_bigru_model.pth')

        cnn_bigru_info = self.models['CNN-BiGRU'].get_model_info()
        print(f"✅ CNN-BiGRU训练完成")
        print(f"   参数量: {cnn_bigru_info['total_params']:,}")
        print(f"   测试RMSE: {self.results['CNN-BiGRU']['test']['RMSE']:.6f}")
        print(f"   模型已保存: {cnn_bigru_model_path}")








    def analyze_advantages(self) -> None:
        """分析各模型的优势"""
        print("\n步骤8: 多模型性能对比分析")
        print("-" * 40)

        # 获取基线性能（如果存在）
        if 'Baseline-BiGRU' in self.results:
            baseline_rmse = self.results['Baseline-BiGRU']['test']['RMSE']
            baseline_r2 = self.results['Baseline-BiGRU']['test']['R2']
            baseline_mae = self.results['Baseline-BiGRU']['test']['MAE']

            print(f"📊 性能对比分析 (以Baseline-BiGRU为基准):")
            print(f"基线BiGRU (无增强):")
            print(f"  RMSE: {baseline_rmse:.6f}")
            print(f"  R²:   {baseline_r2:.6f}")
            print(f"  MAE:  {baseline_mae:.6f}")
        else:
            # 如果没有基线模型，选择第一个可用模型作为参考
            baseline_model = list(self.results.keys())[0]
            baseline_rmse = self.results[baseline_model]['test']['RMSE']
            baseline_r2 = self.results[baseline_model]['test']['R2']
            baseline_mae = self.results[baseline_model]['test']['MAE']

            print(f"📊 性能对比分析 (以{baseline_model}为基准):")
            print(f"{baseline_model} (参考基准):")
            print(f"  RMSE: {baseline_rmse:.6f}")
            print(f"  R²:   {baseline_r2:.6f}")
            print(f"  MAE:  {baseline_mae:.6f}")

        # 分析所有其他模型相对于基线的性能
        for model_name, results in self.results.items():
            if model_name == 'Baseline-BiGRU' or (not 'Baseline-BiGRU' in self.results and model_name == list(self.results.keys())[0]):
                continue  # 跳过基线模型

            model_rmse = results['test']['RMSE']
            model_r2 = results['test']['R2']
            model_mae = results['test']['MAE']

            rmse_improvement = ((baseline_rmse - model_rmse) / baseline_rmse) * 100
            r2_improvement = ((model_r2 - baseline_r2) / baseline_r2) * 100
            mae_improvement = ((baseline_mae - model_mae) / baseline_mae) * 100

            print(f"\n{model_name}:")
            print(f"  RMSE: {model_rmse:.6f} (改进: {rmse_improvement:+.2f}%)")
            print(f"  R²:   {model_r2:.6f} (改进: {r2_improvement:+.2f}%)")
            print(f"  MAE:  {model_mae:.6f} (改进: {mae_improvement:+.2f}%)")

            # 根据模型类型给出技术分析
            if 'DPTAM' in model_name and 'ASB' not in model_name:
                if rmse_improvement > 0:
                    print(f"  ✅ DPTAM时序注意力机制有效！")
                else:
                    print(f"  ⚠️ DPTAM时序注意力机制效果有限")
            elif 'ASB-DPTAM' in model_name:
                if rmse_improvement > 0:
                    print(f"  ✅ ASB+DPTAM串联架构有效！")
                else:
                    print(f"  ⚠️ ASB+DPTAM串联架构效果有限")
            elif 'BiLSTM' in model_name:
                if rmse_improvement > 0:
                    print(f"  ✅ BiLSTM长短期记忆机制有效！")
                else:
                    print(f"  ⚠️ BiLSTM相比BiGRU无明显优势")
            elif 'CNN-BiGRU' in model_name:
                if rmse_improvement > 0:
                    print(f"  ✅ CNN+BiGRU融合架构有效！")
                else:
                    print(f"  ⚠️ CNN特征提取未能显著提升性能")

        # 找出最佳模型
        best_model = min(self.results.keys(), key=lambda x: self.results[x]['test']['RMSE'])
        best_rmse = self.results[best_model]['test']['RMSE']

        print(f"\n🏆 最佳模型: {best_model}")
        print(f"   最佳RMSE: {best_rmse:.6f}")

        # 技术洞察总结
        print(f"\n💡 技术洞察:")
        if 'ASB-DPTAM-BiGRU' in self.results and 'DPTAM-BiGRU' in self.results:
            asb_dptam_rmse = self.results['ASB-DPTAM-BiGRU']['test']['RMSE']
            dptam_rmse = self.results['DPTAM-BiGRU']['test']['RMSE']
            asb_contribution = ((dptam_rmse - asb_dptam_rmse) / dptam_rmse) * 100
            print(f"• ASB频域增强的独立贡献: {asb_contribution:+.2f}%")



        print(f"• 模型复杂度与性能的平衡是关键考虑因素")
        print(f"• 不同架构适合不同的数据特性和应用场景")

    def generate_comparison_report(self) -> None:
        """生成对比报告"""
        print("\n步骤9: 生成对比报告")
        print("-" * 40)

        # 确定基线模型
        if 'Baseline-BiGRU' in self.results:
            baseline_model = 'Baseline-BiGRU'
            baseline_rmse = self.results[baseline_model]['test']['RMSE']
        else:
            baseline_model = list(self.results.keys())[0]
            baseline_rmse = self.results[baseline_model]['test']['RMSE']

        # 计算所有模型相对于基线的改进指标
        improvements = {}
        for model_name in self.results.keys():
            if model_name != baseline_model:
                model_rmse = self.results[model_name]['test']['RMSE']
                improvement = ((baseline_rmse - model_rmse) / baseline_rmse) * 100
                improvements[model_name] = improvement

        # 生成报告内容
        trained_models = list(self.results.keys())
        model_descriptions = {
            'Baseline-BiGRU': '基线双向GRU模型 (无增强)',
            'DPTAM-BiGRU': '加入DPTAM时序注意力机制 (时序增强)',
            'ASB-DPTAM-BiGRU': 'ASB频域增强+DPTAM时序注意力的串联架构 (双重增强)',
            'BiLSTM': '双向LSTM模型 (长短期记忆机制)',
            'CNN-BiGRU': 'CNN特征提取+BiGRU时序建模 (卷积+循环)'
        }

        report_content = f"""# 多模型性能对比分析报告

## 实验概述
本实验对比了{len(trained_models)}种风电功率预测模型的性能：
"""

        for i, model_name in enumerate(trained_models, 1):
            description = model_descriptions.get(model_name, '深度学习模型')
            report_content += f"{i}. **{model_name}**: {description}\n"

        report_content += f"""
## 模型配置
- **序列长度**: {MODEL_CONFIG['sequence_length']}
- **训练轮数**: {MODEL_CONFIG['epochs']}
- **学习率**: {MODEL_CONFIG['learning_rate']}
- **批次大小**: {MODEL_CONFIG['batch_size']}

## 性能对比结果

### 测试集性能指标
| 模型 | RMSE | R² | MAE | 参数量 |
|------|------|----|----|--------|
"""

        # 添加所有模型的性能指标
        for model_name in trained_models:
            results = self.results[model_name]
            model_info = self.models[model_name].get_model_info()
            report_content += f"| {model_name} | {results['test']['RMSE']:.6f} | {results['test']['R2']:.6f} | {results['test']['MAE']:.6f} | {model_info['total_params']:,} |\n"

        report_content += f"""
### 性能改进分析 (相对于{baseline_model})
"""

        for model_name, improvement in improvements.items():
            status = "✅ 有效提升" if improvement > 0 else "⚠️ 未能提升"
            report_content += f"- **{model_name}**: {improvement:+.2f}% {status}\n"

        # 找出最佳模型
        best_model = min(self.results.keys(), key=lambda x: self.results[x]['test']['RMSE'])
        best_rmse = self.results[best_model]['test']['RMSE']

        report_content += f"""
### 最佳模型
🏆 **{best_model}** (RMSE: {best_rmse:.6f})

## 结论

### 1. 模型架构分析
"""

        # 根据实际训练的模型生成相应的分析
        if 'DPTAM-BiGRU' in improvements:
            dptam_status = "✅ 验证成功" if improvements['DPTAM-BiGRU'] > 0 else "❌ 验证失败"
            report_content += f"""
**DPTAM时序注意力机制**: {dptam_status}
- 改进幅度: {improvements['DPTAM-BiGRU']:+.2f}%
- 机制: 分段时序注意力，突出重要时间段
- 效果: {'有效识别时序模式' if improvements['DPTAM-BiGRU'] > 0 else '可能需要调整分段策略或参数'}
"""

        if 'ASB-DPTAM-BiGRU' in improvements:
            asb_status = "✅ 验证成功" if improvements['ASB-DPTAM-BiGRU'] > 0 else "❌ 验证失败"
            report_content += f"""
**ASB+DPTAM串联架构**: {asb_status}
- 改进幅度: {improvements['ASB-DPTAM-BiGRU']:+.2f}%
- 架构: ASB频域降噪 → DPTAM时序注意力 → BiGRU双向建模
- 优势: {'渐进式处理策略有效，先降噪再注意力' if improvements['ASB-DPTAM-BiGRU'] > 0 else '可能需要优化处理顺序或组件参数'}
"""

        if 'BiLSTM' in improvements:
            bilstm_status = "✅ 有效" if improvements['BiLSTM'] > 0 else "⚠️ 效果有限"
            report_content += f"""
**BiLSTM长短期记忆**: {bilstm_status}
- 改进幅度: {improvements['BiLSTM']:+.2f}%
- 机制: 长短期记忆门控机制，更好的梯度流
- 效果: {'相比GRU有明显优势' if improvements['BiLSTM'] > 0 else '在此数据集上未显示明显优势'}
"""

        if 'CNN-BiGRU' in improvements:
            cnn_bigru_status = "✅ 有效" if improvements['CNN-BiGRU'] > 0 else "⚠️ 效果有限"
            report_content += f"""
**CNN-BiGRU融合架构**: {cnn_bigru_status}
- 改进幅度: {improvements['CNN-BiGRU']:+.2f}%
- 机制: CNN特征提取 + BiGRU时序建模
- 效果: {'卷积特征提取有效提升性能' if improvements['CNN-BiGRU'] > 0 else 'CNN特征提取未能显著提升性能'}
"""



        report_content += f"""
### 2. 技术洞察
- **模型复杂度**: 更复杂的模型不一定带来更好的性能
- **架构设计**: 合适的架构设计比单纯增加复杂度更重要
- **数据适配**: 不同模型适合不同的数据特性和应用场景
- **参数效率**: 在性能和计算效率间需要找到平衡点

### 3. 实用建议
- 优先考虑性能最佳的模型: **{best_model}**
- 在资源受限环境下，考虑参数量较少的高效模型
- 根据具体应用场景选择合适的模型架构
- 持续监控模型性能，适时调整超参数

实验时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
实验模型数量: {len(trained_models)}
"""

        # 保存报告
        report_path = os.path.join(get_current_paths()['reports'], 'asb_dptam_advantage_comparison_report.md')
        os.makedirs(os.path.dirname(report_path), exist_ok=True)

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"✅ ASB-DPTAM优势对比报告已保存至: {report_path}")

    def generate_visualizations(self) -> None:
        """生成可视化结果图表"""
        print("\n步骤6: 生成可视化结果")
        print("-" * 40)

        try:
            # 准备models_data格式的数据
            models_data = {}
            for model_name, model_wrapper in self.models.items():
                if model_name in self.results and hasattr(model_wrapper, 'history'):
                    models_data[model_name] = (self.results[model_name], model_wrapper.history)
                    print(f"📊 准备 {model_name} 可视化数据...")

            if models_data:
                # 使用auto_visualize生成标准可视化图表
                print("🎨 生成完整可视化套件...")
                generated_files = auto_visualize(
                    models_data=models_data,
                    experiment_name="ASB_DPTAM_Advantage_Comparison",
                    save_path=get_current_paths()['figures']
                )

                # 生成性能对比图表
                self.create_performance_comparison_plots()

                print("✅ 所有可视化结果已生成")
                print(f"📊 生成的图表文件数量: {len(generated_files.get('universal', [])) + len(generated_files.get('legacy', []))}")
            else:
                print("⚠️ 没有可用的模型数据，跳过可视化")

        except Exception as e:
            print(f"❌ 生成可视化时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def create_performance_comparison_plots(self) -> None:
        """创建性能对比图表"""
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            # 提取性能指标
            models = list(self.results.keys())
            rmse_values = [self.results[model]['test']['RMSE'] for model in models]
            r2_values = [self.results[model]['test']['R2'] for model in models]
            mae_values = [self.results[model]['test']['MAE'] for model in models]

            # 定义颜色方案（支持更多模型）
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
            model_colors = colors[:len(models)]

            # 创建性能对比柱状图
            fig, axes = plt.subplots(1, 3, figsize=(max(15, len(models) * 2), 6))

            # RMSE对比
            bars1 = axes[0].bar(models, rmse_values, color=model_colors)
            axes[0].set_title('RMSE 对比', fontsize=14, fontweight='bold')
            axes[0].set_ylabel('RMSE')
            axes[0].tick_params(axis='x', rotation=45)
            # 添加数值标签
            for bar, value in zip(bars1, rmse_values):
                axes[0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(rmse_values)*0.01,
                           f'{value:.4f}', ha='center', va='bottom', fontsize=8)

            # R²对比
            bars2 = axes[1].bar(models, r2_values, color=model_colors)
            axes[1].set_title('R² 对比', fontsize=14, fontweight='bold')
            axes[1].set_ylabel('R²')
            axes[1].tick_params(axis='x', rotation=45)
            # 添加数值标签
            for bar, value in zip(bars2, r2_values):
                axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(r2_values)*0.01,
                           f'{value:.4f}', ha='center', va='bottom', fontsize=8)

            # MAE对比
            bars3 = axes[2].bar(models, mae_values, color=model_colors)
            axes[2].set_title('MAE 对比', fontsize=14, fontweight='bold')
            axes[2].set_ylabel('MAE')
            axes[2].tick_params(axis='x', rotation=45)
            # 添加数值标签
            for bar, value in zip(bars3, mae_values):
                axes[2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(mae_values)*0.01,
                           f'{value:.4f}', ha='center', va='bottom', fontsize=8)

            plt.tight_layout()

            # 保存图表
            comparison_path = os.path.join(get_current_paths()['figures'], 'performance_comparison.png')
            plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
            plt.close()

            # 创建参数量对比图
            self.create_parameter_comparison_plot(models, model_colors)

            print(f"📊 性能对比图表已保存: {comparison_path}")

        except Exception as e:
            print(f"❌ 创建性能对比图表时发生错误: {str(e)}")

    def create_parameter_comparison_plot(self, models, colors):
        """创建参数量对比图表"""
        try:
            import matplotlib.pyplot as plt

            # 提取参数量信息
            param_counts = [self.models[model].get_model_info()['total_params'] for model in models]

            # 创建参数量对比图
            fig, ax = plt.subplots(1, 1, figsize=(max(10, len(models) * 1.5), 6))

            bars = ax.bar(models, param_counts, color=colors)
            ax.set_title('模型参数量对比', fontsize=14, fontweight='bold')
            ax.set_ylabel('参数数量')
            ax.tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, value in zip(bars, param_counts):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(param_counts)*0.01,
                       f'{value:,}', ha='center', va='bottom', fontsize=8)

            plt.tight_layout()

            # 保存图表
            param_path = os.path.join(get_current_paths()['figures'], 'parameter_comparison.png')
            plt.savefig(param_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"📊 参数量对比图表已保存: {param_path}")

        except Exception as e:
            print(f"❌ 创建参数量对比图表时发生错误: {str(e)}")

    def run_comparison_experiment(self) -> None:
        """运行完整的对比实验"""
        try:
            # 1. 数据准备
            data = self.load_and_prepare_data()

            # 2. 根据配置训练各种模型
            if COMPARISON_EXPERIMENT_CONFIG['enable_baseline_bigru']:
                self.train_baseline_bigru(data)

            if COMPARISON_EXPERIMENT_CONFIG['enable_dptam_bigru']:
                self.train_dptam_bigru(data)

            if COMPARISON_EXPERIMENT_CONFIG['enable_asb_dptam_bigru']:
                self.train_asb_dptam_bigru(data)

            if COMPARISON_EXPERIMENT_CONFIG['enable_bilstm']:
                self.train_bilstm(data)

            if COMPARISON_EXPERIMENT_CONFIG['enable_cnn_bigru']:
                self.train_cnn_bigru(data)

            # 3. 分析优势
            self.analyze_advantages()

            # 4. 生成可视化结果
            self.generate_visualizations()

            # 5. 生成报告
            self.generate_comparison_report()

            print(f"\n🎉 ASB-DPTAM优势对比实验完成！")
            print(f"📁 所有结果已保存到: {get_current_paths()['results_root']}")

            # 实验总结
            print(f"\n📋 实验总结:")
            trained_models = list(self.models.keys())
            for model_name in trained_models:
                print(f"✅ {model_name}: 训练完成")
            print(f"💡 通过对比分析，可以清楚看到每个模型的性能差异和技术优势")

        except Exception as e:
            print(f"\n❌ 实验过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """主函数"""
    comparator = ASBDPTAMAdvantageComparator()
    comparator.run_comparison_experiment()


if __name__ == "__main__":
    main()
