#!/usr/bin/envpython#-*-coding:utf-8-*-"""数据格式转换器自动检查数据文件格式，如果不是CSV格式则转换为CSV并保存在源文件夹中"""importosimportpandasaspdfromtypingimportOptional,Tupleimportwarningswarnings.filterwarnings('ignore')classDataFormatConverter:"""数据格式转换器类"""def__init__(self):"""初始化转换器"""self.supported_formats={'.xlsx':self._read_excel,'.xls':self._read_excel,'.csv':self._read_csv,'.json':self._read_json,'.parquet':self._read_parquet}def_read_excel(self,file_path:str,**kwargs)->pd.DataFrame:"""读取Excel文件"""returnpd.read_excel(file_path,**kwargs)def_read_csv(self,file_path:str,**kwargs)->pd.DataFrame:"""读取CSV文件，尝试多种编码"""encodings=['utf-8','gbk','gb2312','latin-1','cp1252']forencodinginencodings:try:returnpd.read_csv(file_path,encoding=encoding,**kwargs)exceptUnicodeDecodeError:continueraiseValueError(f"无法使用任何编码读取CSV文件:{file_path}")def_read_json(self,file_path:str,**kwargs)->pd.DataFrame:"""读取JSON文件"""returnpd.read_json(file_path,**kwargs)def_read_parquet(self,file_path:str,**kwargs)->pd.DataFrame:"""读取Parquet文件"""returnpd.read_parquet(file_path,**kwargs)defcheck_and_convert_to_csv(self,file_path:str,force_convert:bool=False)->Tuple[str,bool]:"""检查文件格式，如果不是CSV则转换为CSVArgs:file_path:原始文件路径force_convert:是否强制转换（即使已经是CSV格式）Returns:tuple:(csv_file_path,was_converted)-csv_file_path:CSV文件路径-was_converted:是否进行了转换"""ifnotos.path.exists(file_path):raiseFileNotFoundError(f"文件不存在:{file_path}")#获取文件信息file_dir=os.path.dirname(file_path)file_name=os.path.basename(file_path)file_name_without_ext=os.path.splitext(file_name)[0]file_ext=os.path.splitext(file_path)[1].lower()#生成CSV文件路径csv_file_path=os.path.join(file_dir,f"{file_name_without_ext}.csv")#如果已经是CSV格式且不强制转换iffile_ext=='.csv'andnotforce_convert:print(f"文件已经是CSV格式:{file_path}")returnfile_path,False#检查CSV文件是否已存在且比原文件新ifos.path.exists(csv_file_path)andnotforce_convert:original_mtime=os.path.getmtime(file_path)csv_mtime=os.path.getmtime(csv_file_path)ifcsv_mtime>=original_mtime:print(f"CSV文件已存在且是最新的:{csv_file_path}")returncsv_file_path,False#进行格式转换print(f"开始转换文件格式:{file_ext}->.csv")print(f"源文件:{file_path}")print(f"目标文件:{csv_file_path}")try:#检查文件格式是否支持iffile_extnotinself.supported_formats:raiseValueError(f"不支持的文件格式:{file_ext}")#读取原始文件read_func=self.supported_formats[file_ext]df=read_func(file_path)print(f"读取成功，数据形状:{df.shape}")print(f"数据列:{list(df.columns)}")#标准化列名（保持与配置一致）column_mapping={'Time(year-month-dayh:m:s)':'Time(year-month-dayh:m:s)',#保持原名'Power(MW)':'Power(MW)'#保持原名}#应用列名映射（如果需要）df=df.rename(columns=column_mapping)#保存为CSV格式df.to_csv(csv_file_path,index=False,encoding='utf-8')print(f"转换完成:{csv_file_path}")print(f"文件大小:{os.path.getsize(csv_file_path)/1024/1024:.2f}MB")returncsv_file_path,TrueexceptExceptionase:print(f"转换失败:{str(e)}")raisedefconvert_multiple_files(self,file_paths:list,force_convert:bool=False)->dict:"""批量转换多个文件Args:file_paths:文件路径列表force_convert:是否强制转换Returns:dict:转换结果字典{原始路径:(CSV路径,是否转换)}"""results={}print(f"开始批量转换{len(file_paths)}个文件...")fori,file_pathinenumerate(file_paths,1):print(f"\n[{i}/{len(file_paths)}]处理文件:{os.path.basename(file_path)}")try:csv_path,was_converted=self.check_and_convert_to_csv(file_path,force_convert)results[file_path]=(csv_path,was_converted)exceptExceptionase:print(f"处理失败:{str(e)}")results[file_path]=(None,False)#统计结果converted_count=sum(1for_,(_,was_converted)inresults.items()ifwas_converted)success_count=sum(1for_,(csv_path,_)inresults.items()ifcsv_pathisnotNone)print(f"\n批量转换完成:")print(f"总文件数:{len(file_paths)}")print(f"成功处理:{success_count}")print(f"实际转换:{converted_count}")print(f"失败数量:{len(file_paths)-success_count}")returnresultsdefget_file_info(self,file_path:str)->dict:"""获取文件信息Args:file_path:文件路径Returns:dict:文件信息"""ifnotos.path.exists(file_path):return{"exists":False}file_ext=os.path.splitext(file_path)[1].lower()file_size=os.path.getsize(file_path)info={"exists":True,"path":file_path,"extension":file_ext,"size_bytes":file_size,"size_mb":file_size/1024/1024,"supported":file_extinself.supported_formats,"is_csv":file_ext=='.csv'}returninfodefauto_convert_to_csv(file_path:str,force_convert:bool=False)->str:"""便捷函数：自动转换文件为CSV格式Args:file_path:原始文件路径force_convert:是否强制转换Returns:str:CSV文件路径"""converter=DataFormatConverter()csv_path,was_converted=converter.check_and_convert_to_csv(file_path,force_convert)returncsv_pathif__name__=="__main__":#测试代码converter=DataFormatConverter()#示例：转换Excel文件test_file="Windfarmsite1(Nominalcapacity-99MW).xlsx"ifos.path.exists(test_file):csv_path,was_converted=converter.check_and_convert_to_csv(test_file)print(f"结果:{csv_path},是否转换:{was_converted}")