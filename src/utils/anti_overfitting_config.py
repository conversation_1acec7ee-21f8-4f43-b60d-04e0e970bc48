#!/usr/bin/envpython#-*-coding:utf-8-*-"""防过拟合配置针对当前严重过拟合问题的改进配置"""#防过拟合模型配置ANTI_OVERFITTING_CONFIG={#数据配置'sequence_length':24,'train_ratio':0.7,'val_ratio':0.15,'test_ratio':0.15,#训练配置-防过拟合调整'batch_size':64,#增大批次大小，提高泛化能力'epochs':50,#减少训练轮数，防止过度训练'patience':8,#减少耐心值，更早停止'learning_rate':0.001,#提高学习率，避免过度拟合训练数据'weight_decay':1e-4,#添加L2正则化'gradient_clip':1.0,#梯度裁剪#数据增强'use_data_augmentation':True,'noise_factor':0.01,#添加噪声增强泛化'dropout_schedule':True,#动态dropout调整#验证策略'validation_freq':1,#每个epoch都验证'save_best_only':True,#只保存最佳模型'monitor_metric':'val_loss',#监控验证损失'min_delta':1e-4,#最小改进阈值#设备配置'device':'cuda'if__import__('torch').cuda.is_available()else'cpu','random_seed':42}#防过拟合BiGRU配置ANTI_OVERFITTING_BIGRU_CONFIG={'name':'Anti-Overfitting-BiGRU','bigru_units':[64,32],#减少模型复杂度'dense_units':[32,16],#减少全连接层大小'dropout_rate':0.5,#大幅增加dropout'recurrent_dropout':0.3,#添加循环层dropout'bidirectional':True,'batch_norm':True,#添加批归一化'layer_norm':False,#不使用层归一化（避免过度正则化）}#防过拟合DPTAM-BiGRU配置ANTI_OVERFITTING_DPTAM_CONFIG={'name':'Anti-Overfitting-DPTAM-BiGRU','n_segment':4,#减少分段数，降低复杂度'dptam_kernel_size':3,#减少卷积核大小'bigru_units':[64,32],#减少模型复杂度'dense_units':[32,16],#减少全连接层大小'dropout_rate':0.5,#大幅增加dropout'recurrent_dropout':0.3,#添加循环层dropout'attention_dropout':0.4,#注意力层dropout'bidirectional':True,'batch_norm':True,#添加批归一化'fusion_strategy':'serial','regularization_strength':0.01,#注意力正则化强度}#数据预处理防过拟合配置DATA_PREPROCESSING_CONFIG={'feature_selection_method':'correlation',#特征选择方法'max_features':15,#限制特征数量'remove_highly_correlated':True,#移除高相关特征'correlation_threshold':0.95,#相关性阈值'standardization_method':'robust',#使用鲁棒标准化'outlier_removal':True,#移除异常值'outlier_method':'iqr',#异常值检测方法'outlier_factor':1.5,#异常值因子}#训练策略配置TRAINING_STRATEGY_CONFIG={'use_early_stopping':True,'use_reduce_lr_on_plateau':True,'lr_reduction_factor':0.5,'lr_reduction_patience':3,'min_lr':1e-6,'use_model_checkpoint':True,'checkpoint_monitor':'val_loss','checkpoint_mode':'min','use_cross_validation':False,#暂时不使用交叉验证'cv_folds':5,'ensemble_methods':['bagging'],#集成方法'n_estimators':3,#集成模型数量}#评估配置EVALUATION_CONFIG={'metrics':['mae','mse','rmse','r2','mape'],'detailed_analysis':True,'residual_analysis':True,'prediction_intervals':True,'confidence_level':0.95,#过拟合检测'overfitting_detection':True,'train_val_gap_threshold':0.1,#训练-验证差距阈值'r2_drop_threshold':0.15,#R²下降阈值'mape_spike_threshold':500,#MAPE异常阈值}#可视化配置VISUALIZATION_CONFIG={'plot_training_curves':True,'plot_prediction_vs_actual':True,'plot_residuals':True,'plot_feature_importance':True,'plot_attention_weights':True,'save_plots':True,'plot_format':'png','dpi':300,}defget_anti_overfitting_config():"""获取完整的防过拟合配置"""return{'model':ANTI_OVERFITTING_CONFIG,'bigru':ANTI_OVERFITTING_BIGRU_CONFIG,'dptam':ANTI_OVERFITTING_DPTAM_CONFIG,'data':DATA_PREPROCESSING_CONFIG,'training':TRAINING_STRATEGY_CONFIG,'evaluation':EVALUATION_CONFIG,'visualization':VISUALIZATION_CONFIG,}defprint_anti_overfitting_summary():"""打印防过拟合策略总结"""print("🛡️防过拟合策略总结:")print("="*50)print("模型复杂度控制:")print("-减少隐藏层大小:128→64,64→32")print("-增加Dropout:0.2→0.5")print("-添加循环层Dropout:0.3")print("-添加批归一化")print("\n训练策略优化:")print("-减少训练轮数:80→50")print("-提前停止耐心:20→8")print("-增大批次大小:32→64")print("-添加L2正则化:1e-4")print("-梯度裁剪:1.0")print("\n数据处理改进:")print("-特征选择:限制15个特征")print("-移除高相关特征:>0.95")print("-异常值处理:IQR方法")print("-鲁棒标准化")print("\n监控与评估:")print("-过拟合检测阈值:10%")print("-MAPE异常阈值:500%")print("-详细残差分析")print("-预测区间估计")if__name__=="__main__":print_anti_overfitting_summary()config=get_anti_overfitting_config()print(f"\n配置加载完成，包含{len(config)}个模块")