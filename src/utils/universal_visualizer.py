"""通用可视化系统支持任意模型组合的标准化可视化流程"""importmatplotlib.pyplotaspltimportmatplotlib.patchesasmpatchesimportseabornassnsimportnumpyasnpimportpandasaspdfromtypingimportDict,List,Optional,Tuple,Any,Unionimportosimportsysfromsklearn.metricsimportmean_absolute_error,mean_squared_error,r2_scorefrommpl_toolkits.axes_grid1.inset_locatorimportinset_axes#添加项目根目录到系统路径sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))fromsrc.utils.configimportACADEMIC_COLORS,MODEL_COLORS,AUTO_COLOR_POOL,get_current_paths,setup_matplotlib,COMPARISON_EXPERIMENT_CONFIG#优雅浅色方案-真实值浅灰色虚线，模型用浅色实线HIGH_CONTRAST_COLORS={'primary':'#666666',#浅灰色-真实值（虚线）'model1':'#FF9999',#浅红色-Baseline-BiGRU（实线）'model2':'#9999FF',#浅蓝色-DPTAM-BiGRU（实线）'model3':'#FFCC66',#浅黄色-ASB-DPTAM-BiGRU（实线）'model4':'#FF9966',#浅橙色-备用'model5':'#CC99CC',#浅紫色-备用}#线条样式配置-真实值虚线，模型实线，更细的线条LINE_STYLES=['--','-','-','-','-','-','-','-','-','-']#真实值虚线，其他实线LINE_WIDTHS=[2.0,2.0,2.0,2.0,2.0,2.0,2.0,2.0,2.0,2.0]#更细的线宽，支持更多模型classUniversalVisualizer:"""通用可视化系统-支持任意模型组合的标准化可视化"""def__init__(self):"""初始化通用可视化工具"""setup_matplotlib()self._setup_enhanced_font()#增强字体设置self.colors=ACADEMIC_COLORSself.model_colors=MODEL_COLORS.copy()self.auto_color_pool=AUTO_COLOR_POOL.copy()self.used_auto_colors=set()self.high_contrast_colors=HIGH_CONTRAST_COLORSdef_setup_enhanced_font(self):"""增强字体设置，解决方框字问题"""try:#设置中文字体plt.rcParams['font.sans-serif']=['SimHei','MicrosoftYaHei','ArialUnicodeMS','DejaVuSans']plt.rcParams['axes.unicode_minus']=Falseplt.rcParams['font.size']=12plt.rcParams['axes.titlesize']=16plt.rcParams['axes.labelsize']=12plt.rcParams['xtick.labelsize']=10plt.rcParams['ytick.labelsize']=10plt.rcParams['legend.fontsize']=11print("增强字体设置完成")exceptExceptionase:print(f"字体设置失败:{e}")def_get_high_contrast_color(self,model_name:str,index:int=0)->str:"""获取高对比度颜色"""#精确匹配模型名称if'Baseline-BiGRU'inmodel_nameormodel_name=='Baseline-BiGRU':returnself.high_contrast_colors['model1']#亮红色elif'DPTAM-BiGRU'inmodel_nameand'ASB'notinmodel_name:returnself.high_contrast_colors['model2']#亮蓝色elif'ASB-DPTAM-BiGRU'inmodel_nameor'ASB'inmodel_name:returnself.high_contrast_colors['model3']#亮绿色else:#按顺序分配颜色color_keys=['model1','model2','model3','model4','model5']returnself.high_contrast_colors.get(color_keys[index%len(color_keys)],'#000000')def_get_model_color(self,model_name:str)->str:"""获取模型颜色，支持自动分配"""ifmodel_nameinself.model_colors:returnself.model_colors[model_name]#自动分配颜色available_colors=[cforcinself.auto_color_poolifcnotinself.used_auto_colors]ifavailable_colors:color=available_colors[0]self.model_colors[model_name]=colorself.used_auto_colors.add(color)returncolorelse:#如果颜色池用完，使用默认颜色returnself.colors['primary']def_filter_models_by_config(self,model_names:List[str])->List[str]:"""根据配置文件中的模型开关过滤模型列表Args:model_names:原始模型名称列表Returns:过滤后的模型名称列表"""#模型名称到配置键的映射model_config_mapping={'BiGRU':'enable_baseline_bigru','DPTAM-BiGRU':'enable_dptam_bigru','ASB-DPTAM-BiGRU':'enable_asb_dptam_bigru','BiLSTM':'enable_bilstm','CNN-BiLSTM':'enable_cnn_bilstm','CNN-BiLSTM-Attention':'enable_cnn_bilstm_attention','CNNBiLSTMAttentionModel':'enable_cnn_bilstm_attention','CNN-BiGRU':'enable_cnn_bigru','CNN-BiGRU-Attention':'enable_cnn_bigru_attention','CNNBiGRUAttentionModel':'enable_cnn_bigru_attention'}filtered_models=[]formodel_nameinmodel_names:#查找对应的配置键config_key=model_config_mapping.get(model_name)ifconfig_keyisNone:#如果没有找到对应的配置键，默认包含该模型print(f"模型'{model_name}'没有对应的配置开关，默认包含")filtered_models.append(model_name)else:#检查配置开关ifCOMPARISON_EXPERIMENT_CONFIG.get(config_key,False):filtered_models.append(model_name)else:print(f"模型'{model_name}'被配置开关'{config_key}'禁用，跳过")returnfiltered_modelsdefgenerate_complete_visualization_suite(self,results:Dict[str,Dict],histories:Dict[str,Dict],save_path:Optional[str]=None,experiment_name:str="Model_Comparison")->List[str]:"""生成完整的可视化套件Args:results:模型结果字典{model_name:{dataset:metrics,predictions:{dataset:(y_true,y_pred)}}}histories:训练历史字典{model_name:{train_loss:[],val_loss:[],...}}save_path:保存路径experiment_name:实验名称Returns:生成的图片文件路径列表"""ifsave_pathisNone:save_path=get_current_paths()['figures']#创建分类子目录experiment_dir=save_pathos.makedirs(experiment_dir,exist_ok=True)#创建分类文件夹categories={'training_analysis':'训练分析','prediction_comparison':'预测对比','performance_metrics':'性能指标','error_analysis':'ErrorAnalysis','model_analysis':'模型分析','data_distribution':'数据分布'}category_dirs={}forcategory,chinese_nameincategories.items():category_dir=os.path.join(experiment_dir,f"{category}_{chinese_name}")os.makedirs(category_dir,exist_ok=True)category_dirs[category]=category_dirsaved_files=[]model_names=list(results.keys())print(f"\n开始生成{experiment_name}完整可视化套件...")print(f"保存目录:{experiment_dir}")print(f"检测到模型:{','.join(model_names)}")try:#1.训练历史对比图-分解为单独图表ifhistories:training_files=self._plot_training_history_separated(histories,category_dirs['training_analysis'],experiment_name)saved_files.extend(training_files)#2.预测结果对比图-分解为单独图表ifself._check_dataset_exists(results,'test'):prediction_files=self._plot_prediction_comparison_separated(results,'test',category_dirs['prediction_comparison'],experiment_name)saved_files.extend(prediction_files)#3.性能指标对比图-分解为单独图表metrics_files=self._plot_metrics_comparison_separated(results,category_dirs['performance_metrics'],experiment_name)saved_files.extend(metrics_files)#4.误差分析图-分解为单独图表error_files=self._plot_error_analysis_separated(results,category_dirs['error_analysis'],experiment_name)saved_files.extend(error_files)#5.数据分布分析-分解为单独图表distribution_files=self._plot_data_distribution_separated(results,category_dirs['data_distribution'],experiment_name)saved_files.extend(distribution_files)#6.相对百分比误差时间序列图（简化版，100时间步，无插图）ifself._check_dataset_exists(results,'test'):relative_error_file=self._plot_relative_percentage_error_simple(results,'test',category_dirs['error_analysis'],experiment_name)ifrelative_error_file:saved_files.append(relative_error_file)#7.单个模型详细分析图-分解为单独图表formodel_nameinmodel_names:ifmodel_nameinhistories:model_files=self._plot_single_model_analysis_separated(model_name,results[model_name],histories[model_name],category_dirs['model_analysis'],experiment_name)saved_files.extend(model_files)print(f"\n{experiment_name}可视化套件生成完成！")print(f"共生成{len(saved_files)}个图表文件")print("生成的图表:")fori,file_pathinenumerate(saved_files,1):filename=os.path.basename(file_path)print(f"{i:2d}.{filename}")returnsaved_filesexceptExceptionase:print(f"生成可视化套件时出现错误:{str(e)}")importtracebacktraceback.print_exc()returnsaved_filesdef_check_dataset_exists(self,results:Dict,dataset:str)->bool:"""检查数据集是否存在于所有模型中"""formodel_name,model_resultsinresults.items():if'predictions'inmodel_resultsanddatasetinmodel_results['predictions']:returnTruereturnFalsedef_plot_training_history_universal(self,histories:Dict,save_dir:str,experiment_name:str)->str:"""通用训练历史对比图"""fig,axes=plt.subplots(1,2,figsize=(16,6))#损失函数对比ax1=axes[0]formodel_name,historyinhistories.items():color=self._get_model_color(model_name)ifisinstance(history,dict):epochs=range(1,len(history['train_loss'])+1)train_loss=history['train_loss']val_loss=history['val_loss']else:epochs=range(1,len(history.history['loss'])+1)train_loss=history.history['loss']val_loss=history.history['val_loss']ax1.plot(epochs,train_loss,label=f'{model_name}TrainingLoss',color=color,linewidth=2.5,alpha=0.8)ax1.plot(epochs,val_loss,label=f'{model_name}ValidationLoss',color=color,linewidth=2.5,linestyle='--',alpha=0.8)ax1.set_title('ModelTrainingLossComparison',fontsize=14,fontweight='bold')ax1.set_xlabel('Epochs')ax1.set_ylabel('Loss(MSE)')ax1.legend(frameon=True,fancybox=True,shadow=True)ax1.grid(True,alpha=0.3)#MAE对比ax2=axes[1]formodel_name,historyinhistories.items():color=self._get_model_color(model_name)ifisinstance(history,dict):epochs=range(1,len(history['train_mae'])+1)train_mae=history['train_mae']val_mae=history['val_mae']else:epochs=range(1,len(history.history['mae'])+1)train_mae=history.history['mae']val_mae=history.history['val_mae']ax2.plot(epochs,train_mae,label=f'{model_name}TrainingMAE',color=color,linewidth=2.5,alpha=0.8)ax2.plot(epochs,val_mae,label=f'{model_name}ValidationMAE',color=color,linewidth=2.5,linestyle='--',alpha=0.8)ax2.set_title('ModelTrainingMAEComparison',fontsize=14,fontweight='bold')ax2.set_xlabel('Epochs')ax2.set_ylabel('MeanAbsoluteError(MAE)')ax2.legend(frameon=True,fancybox=True,shadow=True)ax2.grid(True,alpha=0.3)plt.suptitle('TrainingHistoryComparison',fontsize=16,fontweight='bold')plt.tight_layout()save_file=os.path.join(save_dir,'Training_History_Comparison.png')plt.savefig(save_file,dpi=300,bbox_inches='tight')plt.close()print(f"训练历史对比图已保存:{os.path.basename(save_file)}")returnsave_filedef_plot_training_history_separated(self,histories:Dict,save_dir:str,experiment_name:str)->List[str]:"""分离式训练历史图表"""saved_files=[]#1.损失函数对比图plt.figure(figsize=(12,8))fori,(model_name,history)inenumerate(histories.items()):#检查训练历史的键名格式train_loss_key='train_loss'if'train_loss'inhistoryelse'loss'val_loss_key='val_loss'if'val_loss'inhistoryelse'val_loss'iftrain_loss_keyinhistory:epochs=range(1,len(history[train_loss_key])+1)model_color=self._get_high_contrast_color(model_name,i)plt.plot(epochs,history[train_loss_key],label=f'{model_name}-TrainingLoss',linewidth=LINE_WIDTHS[min(i+1,len(LINE_WIDTHS)-1)],alpha=1.0,color=model_color,linestyle='-')#实线ifval_loss_keyinhistory:plt.plot(epochs,history[val_loss_key],label=f'{model_name}-ValidationLoss',linewidth=LINE_WIDTHS[min(i+1,len(LINE_WIDTHS)-1)],alpha=1.0,color=model_color,linestyle='--')#虚线plt.title('TrainingLossComparison',fontsize=16,fontweight='bold')plt.xlabel('Epochs',fontsize=12)plt.ylabel('Loss',fontsize=12)plt.legend(fontsize=10)plt.grid(True,alpha=0.3)plt.tight_layout()loss_file=os.path.join(save_dir,'Training_Loss_Comparison.png')plt.savefig(loss_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(loss_file)print(f"训练损失对比图已保存:{os.path.basename(loss_file)}")#2.MAE指标对比图plt.figure(figsize=(12,8))fori,(model_name,history)inenumerate(histories.items()):#检查MAE历史的键名格式train_mae_key='train_mae'if'train_mae'inhistoryelse'mae'val_mae_key='val_mae'if'val_mae'inhistoryelse'val_mae'iftrain_mae_keyinhistory:epochs=range(1,len(history[train_mae_key])+1)model_color=self._get_high_contrast_color(model_name,i)plt.plot(epochs,history[train_mae_key],label=f'{model_name}-TrainingMAE',linewidth=LINE_WIDTHS[i+1],alpha=1.0,color=model_color,linestyle='-')#实线ifval_mae_keyinhistory:plt.plot(epochs,history[val_mae_key],label=f'{model_name}-ValidationMAE',linewidth=LINE_WIDTHS[i+1],alpha=1.0,color=model_color,linestyle='--')#虚线plt.title('MAEMetricsComparison',fontsize=16,fontweight='bold')plt.xlabel('Epochs',fontsize=12)plt.ylabel('MAE',fontsize=12)plt.legend(fontsize=10)plt.grid(True,alpha=0.3)plt.tight_layout()mae_file=os.path.join(save_dir,'MAE_Metrics_Comparison.png')plt.savefig(mae_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(mae_file)print(f"MAE指标对比图已保存:{os.path.basename(mae_file)}")returnsaved_filesdef_plot_prediction_comparison_universal(self,results:Dict,dataset:str,save_dir:str,experiment_name:str)->str:"""通用预测结果对比图"""model_names=[namefornameinresults.keys()if'predictions'inresults[name]anddatasetinresults[name]['predictions']]ifnotmodel_names:print(f"数据集{dataset}不存在预测结果，跳过")return""n_models=len(model_names)fig,axes=plt.subplots(2,n_models,figsize=(6*n_models,10))ifn_models==1:axes=axes.reshape(2,1)#第一行：散点图对比fori,model_nameinenumerate(model_names):ax=axes[0,i]ifn_models>1elseaxes[0]y_true,y_pred=results[model_name]['predictions'][dataset]#随机采样显示sample_size=min(500,len(y_true))iflen(y_true)>sample_size:indices=np.random.choice(len(y_true),sample_size,replace=False)y_true_sample=y_true[indices]y_pred_sample=y_pred[indices]else:y_true_sample=y_truey_pred_sample=y_predcolor=self._get_model_color(model_name)ax.scatter(y_true_sample,y_pred_sample,alpha=0.6,s=20,color=color,edgecolors='white',linewidth=0.5)#完美预测线min_val=min(y_true_sample.min(),y_pred_sample.min())max_val=max(y_true_sample.max(),y_pred_sample.max())ax.plot([min_val,max_val],[min_val,max_val],'r--',linewidth=2,alpha=0.8,label='PerfectPrediction')ax.set_xlabel('TrueValues(MW)')ax.set_ylabel('PredictedValues(MW)')ax.set_title(f'{model_name}-PredictionsvsTrueValues')ax.legend()#添加R^2值r2=r2_score(y_true,y_pred)ax.text(0.05,0.95,f'R^2={r2:.3f}',transform=ax.transAxes,bbox=dict(boxstyle='round',facecolor='white',alpha=0.8),fontsize=10,fontweight='bold')#第二行：时间序列对比display_points=min(300,min([len(results[name]['predictions'][dataset][0])fornameinmodel_names]))fori,model_nameinenumerate(model_names):ax=axes[1,i]ifn_models>1elseaxes[1]y_true,y_pred=results[model_name]['predictions'][dataset]time_steps=range(display_points)ax.plot(time_steps,y_true[:display_points],label='TrueValues',color='#212121',linewidth=2.5,alpha=1.0)color=self._get_model_color(model_name)ax.plot(time_steps,y_pred[:display_points],label=f'{model_name}Predictions',color=color,linewidth=2.5,alpha=0.9)ax.set_title(f'{model_name}-TimeSeriesPredictionComparison')ax.set_xlabel('TimeSteps')ax.set_ylabel('Power(MW)')ax.legend()ax.grid(True,alpha=0.3)plt.suptitle(f'{dataset.upper()}SetPredictionResultsComparison',fontsize=16,fontweight='bold')plt.tight_layout()save_file=os.path.join(save_dir,f'{dataset.upper()}_Set_Prediction_Results_Comparison.png')plt.savefig(save_file,dpi=300,bbox_inches='tight')plt.close()print(f"{dataset}setpredictioncomparisonsaved:{os.path.basename(save_file)}")returnsave_filedef_plot_prediction_comparison_separated(self,results:Dict,dataset:str,save_dir:str,experiment_name:str)->List[str]:"""分离式预测对比图表"""saved_files=[]model_names=[namefornameinresults.keys()if'predictions'inresults[name]anddatasetinresults[name]['predictions']]ifnotmodel_names:returnsaved_files#获取第一个模型的真实值y_true,_=results[model_names[0]]['predictions'][dataset]display_points=min(500,len(y_true))time_steps=range(display_points)#1.整体预测对比图plt.figure(figsize=(16,10))plt.plot(time_steps,y_true[:display_points],label='TrueValues',color=self.high_contrast_colors['primary'],linewidth=LINE_WIDTHS[0],alpha=1.0,linestyle='--')#真实值固定为虚线fori,model_nameinenumerate(model_names):_,y_pred=results[model_name]['predictions'][dataset]color=self._get_high_contrast_color(model_name,i)plt.plot(time_steps,y_pred[:display_points],label=f'{model_name}Predictions',color=color,linewidth=LINE_WIDTHS[min(i+1,len(LINE_WIDTHS)-1)],alpha=1.0,linestyle='-')#模型预测值固定为实线plt.title(f'{dataset.upper()}SetPredictionResultsComparison',fontsize=16,fontweight='bold')plt.xlabel('TimeSteps',fontsize=12)plt.ylabel('Power(Normalized)',fontsize=12)plt.legend(fontsize=11)plt.grid(True,alpha=0.3)plt.tight_layout()overall_file=os.path.join(save_dir,f'{dataset.upper()}_Set_Overall_Prediction_Comparison.png')plt.savefig(overall_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(overall_file)print(f"{dataset}setoverallpredictioncomparisonsaved:{os.path.basename(overall_file)}")#2.局部细节对比图（前100个点）detail_points=min(100,len(y_true))detail_steps=range(detail_points)plt.figure(figsize=(16,10))plt.plot(detail_steps,y_true[:detail_points],label='TrueValues',color=self.high_contrast_colors['primary'],linewidth=LINE_WIDTHS[0],alpha=1.0,marker='o',markersize=5,linestyle='--')#真实值固定为虚线markers=['s','^','D','v','p']fori,model_nameinenumerate(model_names):_,y_pred=results[model_name]['predictions'][dataset]color=self._get_high_contrast_color(model_name,i)plt.plot(detail_steps,y_pred[:detail_points],label=f'{model_name}Predictions',color=color,linewidth=LINE_WIDTHS[min(i+1,len(LINE_WIDTHS)-1)],alpha=1.0,marker=markers[i%len(markers)],markersize=4,linestyle='-')#模型预测值固定为实线plt.title(f'{dataset.upper()}SetPredictionDetailsComparison(First{detail_points}TimeSteps)',fontsize=16,fontweight='bold')plt.xlabel('TimeSteps',fontsize=12)plt.ylabel('Power(Normalized)',fontsize=12)plt.legend(fontsize=11)plt.grid(True,alpha=0.3)plt.tight_layout()detail_file=os.path.join(save_dir,f'{dataset.upper()}_Set_Prediction_Details_Comparison.png')plt.savefig(detail_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(detail_file)print(f"{dataset}setpredictiondetailscomparisonsaved:{os.path.basename(detail_file)}")returnsaved_filesdef_plot_combined_time_series_universal(self,results:Dict,save_dir:str,experiment_name:str)->str:"""通用综合时间序列对比图"""#使用测试集数据dataset='test'model_names=[namefornameinresults.keys()if'predictions'inresults[name]anddatasetinresults[name]['predictions']]ifnotmodel_names:print(f"没有找到测试集预测结果，跳过综合时间序列图")return""fig,(ax1,ax2)=plt.subplots(2,1,figsize=(16,10))#获取第一个模型的真实值（所有模型的真实值应该相同）y_true,_=results[model_names[0]]['predictions'][dataset]display_points=min(300,len(y_true))time_steps=range(display_points)#第一个子图：完整对比ax1.plot(time_steps,y_true[:display_points],label='OriginalPower',color='#212121',linewidth=3,alpha=1.0,zorder=len(model_names)+1)fori,model_nameinenumerate(model_names):_,y_pred=results[model_name]['predictions'][dataset]color=self._get_model_color(model_name)ax1.plot(time_steps,y_pred[:display_points],label=f'{model_name}Predictions',color=color,linewidth=2.5,alpha=0.9,zorder=len(model_names)-i)ax1.set_title('WindPowerPredictionComprehensiveComparison',fontsize=14,fontweight='bold')ax1.set_xlabel('TimeSteps')ax1.set_ylabel('Power(Normalized)')ax1.legend(frameon=True,fancybox=True,shadow=True,loc='upperright')ax1.grid(True,alpha=0.3)#添加性能指标文本metrics_text=""formodel_nameinmodel_names:_,y_pred=results[model_name]['predictions'][dataset]mae=mean_absolute_error(y_true,y_pred)mse=mean_squared_error(y_true,y_pred)r2=r2_score(y_true,y_pred)metrics_text+=f'{model_name}:MAE={mae:.4f},MSE={mse:.4f},R^2={r2:.3f}\n'ax1.text(0.02,0.98,metrics_text.strip(),transform=ax1.transAxes,bbox=dict(boxstyle='round',facecolor='white',alpha=0.9),fontsize=10,verticalalignment='top',fontfamily='monospace')#第二个子图：误差对比formodel_nameinmodel_names:_,y_pred=results[model_name]['predictions'][dataset]error=y_pred[:display_points]-y_true[:display_points]color=self._get_model_color(model_name)ax2.plot(time_steps,error,label=f'{model_name}PredictionError',color=color,linewidth=2,alpha=0.9)ax2.axhline(y=0,color='#FF5722',linestyle='--',linewidth=2.5,alpha=0.8)ax2.set_title('PredictionErrorComparison',fontsize=14,fontweight='bold')ax2.set_xlabel('TimeSteps')ax2.set_ylabel('PredictionError(Normalized)')ax2.legend(frameon=True,fancybox=True,shadow=True)ax2.grid(True,alpha=0.3)#添加误差统计信息error_text="MeanAbsoluteError:\n"formodel_nameinmodel_names:_,y_pred=results[model_name]['predictions'][dataset]error=y_pred[:display_points]-y_true[:display_points]error_mean=np.mean(np.abs(error))error_text+=f'{model_name}:{error_mean:.4f}\n'ax2.text(0.98,0.98,error_text.strip(),transform=ax2.transAxes,bbox=dict(boxstyle='round',facecolor='white',alpha=0.9),fontsize=10,verticalalignment='top',horizontalalignment='right',fontfamily='monospace')plt.suptitle('ComprehensiveTimeSeriesPredictionComparison',fontsize=16,fontweight='bold')plt.tight_layout()save_file=os.path.join(save_dir,'Comprehensive_Time_Series_Comparison.png')plt.savefig(save_file,dpi=300,bbox_inches='tight')plt.close()print(f"综合时间序列对比图已保存:{os.path.basename(save_file)}")returnsave_filedef_plot_metrics_comparison_universal(self,results:Dict,save_dir:str,experiment_name:str)->str:"""通用评估指标对比图"""metrics=['MAE','MSE','R2','MAPE']datasets=['train','val','test']#检查哪些数据集存在available_datasets=[]fordatasetindatasets:ifany(datasetinresults[model].get('predictions',{})formodelinresults.keys()):available_datasets.append(dataset)ifnotavailable_datasets:print("没有找到可用的数据集，跳过指标对比图")return""model_names=list(results.keys())n_metrics=len(metrics)fig,axes=plt.subplots(2,3,figsize=(18,10))axes=axes.flatten()x=np.arange(len(model_names))width=0.25fori,metricinenumerate(metrics):ax=axes[i]forj,datasetinenumerate(available_datasets):values=[]formodelinmodel_names:ifdatasetinresults[model]:values.append(results[model][dataset].get(metric,0))else:values.append(0)#选择颜色ifdataset=='train':color=self.colors['primary']elifdataset=='val':color=self.colors['secondary']else:color=self.colors['accent']bars=ax.bar(x+j*width,values,width,label=f'{dataset.capitalize()}Set',color=color,alpha=0.8,edgecolor='white')#添加数值标签forbar,valueinzip(bars,values):ifvalue>0:#只显示有效值height=bar.get_height()ax.text(bar.get_x()+bar.get_width()/2.,height,f'{value:.3f}',ha='center',va='bottom',fontsize=8)ax.set_xlabel('Models')ax.set_ylabel(metric)ax.set_title(f'{metric}对比')ax.set_xticks(x+width)ax.set_xticklabels(model_names,rotation=45,ha='right')ax.legend()ax.grid(True,alpha=0.3,axis='y')#隐藏最后一个子图axes[-1].set_visible(False)plt.suptitle('EvaluationMetricsComparison',fontsize=16,fontweight='bold')plt.tight_layout()save_file=os.path.join(save_dir,'Evaluation_Metrics_Comparison.png')plt.savefig(save_file,dpi=300,bbox_inches='tight')plt.close()print(f"评估指标对比图已保存:{os.path.basename(save_file)}")returnsave_filedef_plot_metrics_comparison_separated(self,results:Dict,save_dir:str,experiment_name:str)->List[str]:"""分离式性能指标对比图表"""saved_files=[]model_names=list(results.keys())ifnotmodel_names:returnsaved_files#准备数据metrics_data={}formodel_nameinmodel_names:if'test'inresults[model_name]:metrics_data[model_name]=results[model_name]['test']ifnotmetrics_data:returnsaved_files#获取性能指标专用浅色bar_colors=[]fori,modelinenumerate(model_names):if'Baseline-BiGRU'inmodelormodel=='Baseline-BiGRU':bar_colors.append('#FFB3B3')#浅红色elif'DPTAM-BiGRU'inmodeland'ASB'notinmodel:bar_colors.append('#B3D9FF')#浅蓝色elif'ASB-DPTAM-BiGRU'inmodelor'ASB'inmodel:bar_colors.append('#B3FFB3')#浅绿色else:bar_colors.append('#CCCCCC')#灰色备用#1.RMSE对比图plt.figure(figsize=(12,8))rmse_values=[metrics_data[model]['RMSE']formodelinmodel_namesifmodelinmetrics_data]bars=plt.bar(model_names,rmse_values,color=bar_colors,alpha=0.8,edgecolor='white',linewidth=2)plt.title('RMSEPerformanceComparison',fontsize=16,fontweight='bold')plt.ylabel('RMSE值',fontsize=12)plt.xticks(rotation=45,ha='right')#添加数值标签forbar,valueinzip(bars,rmse_values):height=bar.get_height()plt.text(bar.get_x()+bar.get_width()/2.,height,f'{value:.4f}',ha='center',va='bottom',fontsize=11,fontweight='bold')plt.grid(True,alpha=0.3,axis='y')plt.tight_layout()rmse_file=os.path.join(save_dir,'RMSE_Performance_Comparison.png')plt.savefig(rmse_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(rmse_file)print(f"RMSE性能对比图已保存:{os.path.basename(rmse_file)}")#2.R^2对比图plt.figure(figsize=(12,8))r2_values=[metrics_data[model].get('R^2',metrics_data[model].get('R2',0))formodelinmodel_namesifmodelinmetrics_data]bars=plt.bar(model_names,r2_values,color=bar_colors,alpha=0.8,edgecolor='white',linewidth=2)plt.title('R^2PerformanceComparison',fontsize=16,fontweight='bold')plt.ylabel('R^2Value',fontsize=12)plt.xticks(rotation=45,ha='right')#添加数值标签forbar,valueinzip(bars,r2_values):height=bar.get_height()plt.text(bar.get_x()+bar.get_width()/2.,height,f'{value:.4f}',ha='center',va='bottom',fontsize=11,fontweight='bold')plt.grid(True,alpha=0.3,axis='y')plt.tight_layout()r2_file=os.path.join(save_dir,'R2_Performance_Comparison.png')plt.savefig(r2_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(r2_file)print(f"R^2性能对比图已保存:{os.path.basename(r2_file)}")#3.MAE对比图plt.figure(figsize=(12,8))mae_values=[metrics_data[model]['MAE']formodelinmodel_namesifmodelinmetrics_data]bars=plt.bar(model_names,mae_values,color=bar_colors,alpha=0.8,edgecolor='white',linewidth=2)plt.title('MAEPerformanceComparison',fontsize=16,fontweight='bold')plt.ylabel('MAE',fontsize=12)plt.xticks(rotation=45,ha='right')#添加数值标签forbar,valueinzip(bars,mae_values):height=bar.get_height()plt.text(bar.get_x()+bar.get_width()/2.,height,f'{value:.4f}',ha='center',va='bottom',fontsize=11,fontweight='bold')plt.grid(True,alpha=0.3,axis='y')plt.tight_layout()mae_file=os.path.join(save_dir,'MAE_Performance_Comparison.png')plt.savefig(mae_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(mae_file)print(f"MAE性能对比图已保存:{os.path.basename(mae_file)}")returnsaved_filesdef_plot_error_analysis_universal(self,results:Dict,save_dir:str,experiment_name:str)->str:"""通用误差分析图"""dataset='test'model_names=[namefornameinresults.keys()if'predictions'inresults[name]anddatasetinresults[name]['predictions']]ifnotmodel_names:print(f"没有找到测试集预测结果，跳过误差分析图")return""n_models=len(model_names)fig,axes=plt.subplots(2,n_models,figsize=(6*n_models,10))ifn_models==1:axes=axes.reshape(2,1)fori,model_nameinenumerate(model_names):y_true,y_pred=results[model_name]['predictions'][dataset]errors=y_pred-y_truecolor=self._get_model_color(model_name)#误差分布直方图ax1=axes[0,i]ifn_models>1elseaxes[0]ax1.hist(errors,bins=50,alpha=0.7,color=color,edgecolor='white',density=True)ax1.axvline(x=0,color='red',linestyle='--',linewidth=2)ax1.set_title(f'{model_name}-ErrorDistribution')ax1.set_xlabel('PredictionError(MW)')ax1.set_ylabel('Density')ax1.grid(True,alpha=0.3)#添加统计信息mean_error=np.mean(errors)std_error=np.std(errors)ax1.text(0.05,0.95,f'Mean:{mean_error:.3f}\nStdDev:{std_error:.3f}',transform=ax1.transAxes,bbox=dict(boxstyle='round',facecolor='white',alpha=0.8),fontsize=10,verticalalignment='top')#误差vs真实值ax2=axes[1,i]ifn_models>1elseaxes[1]ax2.scatter(y_true,errors,alpha=0.6,s=15,color=color,edgecolors='white',linewidth=0.5)ax2.axhline(y=0,color='red',linestyle='--',linewidth=2)ax2.set_title(f'{model_name}-ErrorvsTrueValues')ax2.set_xlabel('TrueValues(MW)')ax2.set_ylabel('PredictionError(MW)')ax2.grid(True,alpha=0.3)plt.suptitle('ErrorAnalysis',fontsize=16,fontweight='bold')plt.tight_layout()save_file=os.path.join(save_dir,'Error_Analysis.png')plt.savefig(save_file,dpi=300,bbox_inches='tight')plt.close()print(f"误差分析图已保存:{os.path.basename(save_file)}")returnsave_filedef_plot_error_analysis_separated(self,results:Dict,save_dir:str,experiment_name:str)->List[str]:"""分离式误差分析图表"""saved_files=[]model_names=[namefornameinresults.keys()if'predictions'inresults[name]and'test'inresults[name]['predictions']]ifnotmodel_names:returnsaved_files#为每个模型生成单独的误差分析图fori,model_nameinenumerate(model_names):y_true,y_pred=results[model_name]['predictions']['test']errors=y_pred-y_truemodel_color=self._get_high_contrast_color(model_name,i)#1.误差分布直方图plt.figure(figsize=(12,8))plt.hist(errors,bins=50,alpha=0.7,color=model_color,edgecolor='white',density=True,linewidth=1.5)plt.axvline(x=0,color='red',linestyle='--',linewidth=3,alpha=0.8)plt.title(f'{model_name}-PredictionErrorDistribution',fontsize=16,fontweight='bold')plt.xlabel('PredictionError',fontsize=12)plt.ylabel('Density',fontsize=12)plt.grid(True,alpha=0.3)#添加统计信息mean_error=np.mean(errors)std_error=np.std(errors)plt.text(0.05,0.95,f'Mean:{mean_error:.4f}\nStdDev:{std_error:.4f}',transform=plt.gca().transAxes,bbox=dict(boxstyle='round',facecolor='white',alpha=0.8),fontsize=12,verticalalignment='top',fontweight='bold')plt.tight_layout()error_dist_file=os.path.join(save_dir,f'{model_name}_Error_Distribution.png')plt.savefig(error_dist_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(error_dist_file)print(f"{model_name}errordistributionsaved:{os.path.basename(error_dist_file)}")#2.预测值vs真实值散点图plt.figure(figsize=(10,10))plt.scatter(y_true,y_pred,alpha=0.6,color=model_color,s=20)#添加完美预测线min_val=min(np.min(y_true),np.min(y_pred))max_val=max(np.max(y_true),np.max(y_pred))plt.plot([min_val,max_val],[min_val,max_val],'r--',linewidth=3,alpha=0.8,label='PerfectPredictionLine')plt.title(f'{model_name}-PredictionsvsTrueValues',fontsize=16,fontweight='bold')plt.xlabel('TrueValues',fontsize=12)plt.ylabel('PredictedValues',fontsize=12)plt.legend(fontsize=12)plt.grid(True,alpha=0.3)#添加R^2值fromsklearn.metricsimportr2_scorer2=r2_score(y_true,y_pred)plt.text(0.05,0.95,f'R^2={r2:.4f}',transform=plt.gca().transAxes,bbox=dict(boxstyle='round',facecolor='white',alpha=0.8),fontsize=12,fontweight='bold')plt.tight_layout()scatter_file=os.path.join(save_dir,f'{model_name}_Prediction_Scatter_Plot.png')plt.savefig(scatter_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(scatter_file)print(f"{model_name}predictionscatterplotsaved:{os.path.basename(scatter_file)}")returnsaved_filesdef_plot_performance_radar_universal(self,results:Dict,save_dir:str,experiment_name:str)->str:"""通用性能雷达图"""model_names=list(results.keys())dataset='test'#检查是否有测试集结果ifnotany(datasetinresults[model]formodelinmodel_names):print(f"没有找到测试集结果，跳过性能雷达图")return""#定义评估指标（需要标准化）metrics=['MAE','MSE','R2','MAPE']available_metrics=[]#检查哪些指标可用formetricinmetrics:ifany(metricinresults[model].get(dataset,{})formodelinmodel_names):available_metrics.append(metric)iflen(available_metrics)<3:print(f"可用指标不足，跳过性能雷达图")return""#准备数据angles=np.linspace(0,2*np.pi,len(available_metrics),endpoint=False).tolist()angles+=angles[:1]#闭合雷达图fig,ax=plt.subplots(figsize=(10,10),subplot_kw=dict(projection='polar'))formodel_nameinmodel_names:ifdatasetnotinresults[model_name]:continuevalues=[]formetricinavailable_metrics:value=results[model_name][dataset].get(metric,0)#标准化处理（R2越大越好，其他指标越小越好）ifmetric=='R2':normalized_value=value#R2已经在0-1之间else:#对于误差指标，转换为性能分数（1-normalized_error）max_val=max([results[m][dataset].get(metric,0)forminmodel_namesifdatasetinresults[m]])ifmax_val>0:normalized_value=1-(value/max_val)else:normalized_value=0values.append(normalized_value)values+=values[:1]#闭合雷达图color=self._get_model_color(model_name)ax.plot(angles,values,'o-',linewidth=2,label=model_name,color=color)ax.fill(angles,values,alpha=0.25,color=color)#设置标签ax.set_xticks(angles[:-1])ax.set_xticklabels(available_metrics)ax.set_ylim(0,1)ax.set_title('ModelPerformanceRadarChart',size=16,fontweight='bold',pad=20)ax.legend(loc='upperright',bbox_to_anchor=(1.3,1.0))ax.grid(True)plt.tight_layout()save_file=os.path.join(save_dir,'Performance_Radar_Chart.png')plt.savefig(save_file,dpi=300,bbox_inches='tight')plt.close()print(f"Performanceradarchartsaved:{os.path.basename(save_file)}")returnsave_filedef_plot_single_model_analysis_universal(self,model_name:str,results:Dict,history:Dict,save_dir:str,experiment_name:str)->str:"""通用单个模型详细分析图"""fig=plt.figure(figsize=(16,12))gs=fig.add_gridspec(3,2,height_ratios=[1,1,1],hspace=0.3,wspace=0.3)model_color=self._get_model_color(model_name)#1.训练历史-损失ax1=fig.add_subplot(gs[0,0])ifisinstance(history,dict):epochs=range(1,len(history['train_loss'])+1)ax1.plot(epochs,history['train_loss'],label='TrainingLoss',color=model_color,linewidth=2,alpha=0.8)ax1.plot(epochs,history['val_loss'],label='ValidationLoss',color=model_color,linewidth=2,linestyle='--',alpha=0.8)ax1.set_title(f'{model_name}-TrainingLoss',fontsize=12,fontweight='bold')ax1.set_xlabel('Epochs')ax1.set_ylabel('Loss(MSE)')ax1.legend()ax1.grid(True,alpha=0.3)#2.训练历史-MAEax2=fig.add_subplot(gs[0,1])ifisinstance(history,dict):ax2.plot(epochs,history['train_mae'],label='TrainingMAE',color=model_color,linewidth=2,alpha=0.8)ax2.plot(epochs,history['val_mae'],label='ValidationMAE',color=model_color,linewidth=2,linestyle='--',alpha=0.8)ax2.set_title(f'{model_name}-TrainingMAE',fontsize=12,fontweight='bold')ax2.set_xlabel('训练轮次')ax2.set_ylabel('平均绝对误差')ax2.legend()ax2.grid(True,alpha=0.3)#3.预测vs真实值散点图ax3=fig.add_subplot(gs[1,0])if'predictions'inresultsand'test'inresults['predictions']:y_true,y_pred=results['predictions']['test']#随机采样sample_size=min(500,len(y_true))iflen(y_true)>sample_size:indices=np.random.choice(len(y_true),sample_size,replace=False)y_true_sample=y_true[indices]y_pred_sample=y_pred[indices]else:y_true_sample=y_truey_pred_sample=y_predax3.scatter(y_true_sample,y_pred_sample,alpha=0.6,s=20,color=model_color,edgecolors='white',linewidth=0.5)#完美预测线min_val=min(y_true_sample.min(),y_pred_sample.min())max_val=max(y_true_sample.max(),y_pred_sample.max())ax3.plot([min_val,max_val],[min_val,max_val],'r--',linewidth=2,alpha=0.8,label='PerfectPrediction')ax3.set_xlabel('TrueValues')ax3.set_ylabel('PredictedValues')ax3.set_title(f'{model_name}-PredictionsvsTrueValues')ax3.legend()#添加R^2值r2=r2_score(y_true,y_pred)ax3.text(0.05,0.95,f'R^2={r2:.3f}',transform=ax3.transAxes,bbox=dict(boxstyle='round',facecolor='white',alpha=0.8),fontsize=10,fontweight='bold')#4.时间序列预测对比ax4=fig.add_subplot(gs[1,1])if'predictions'inresultsand'test'inresults['predictions']:y_true,y_pred=results['predictions']['test']display_points=min(300,len(y_true))time_steps=range(display_points)ax4.plot(time_steps,y_true[:display_points],label='TrueValues',color='#212121',linewidth=2.5,alpha=1.0)ax4.plot(time_steps,y_pred[:display_points],label=f'{model_name}Predictions',color=model_color,linewidth=2.5,alpha=0.9)ax4.set_title(f'{model_name}-TimeSeriesPredictionComparison')ax4.set_xlabel('TimeSteps')ax4.set_ylabel('Power(Normalized)')ax4.legend()ax4.grid(True,alpha=0.3)#5.误差分布ax5=fig.add_subplot(gs[2,0])if'predictions'inresultsand'test'inresults['predictions']:y_true,y_pred=results['predictions']['test']errors=y_pred-y_trueax5.hist(errors,bins=50,alpha=0.7,color=model_color,edgecolor='white',density=True)ax5.axvline(x=0,color='red',linestyle='--',linewidth=2)ax5.set_title(f'{model_name}-ErrorDistribution')ax5.set_xlabel('PredictionError')ax5.set_ylabel('Density')ax5.grid(True,alpha=0.3)#添加统计信息mean_error=np.mean(errors)std_error=np.std(errors)ax5.text(0.05,0.95,f'Mean:{mean_error:.4f}\nStdDev:{std_error:.4f}',transform=ax5.transAxes,bbox=dict(boxstyle='round',facecolor='white',alpha=0.8),fontsize=10,verticalalignment='top')#6.性能指标柱状图ax6=fig.add_subplot(gs[2,1])if'test'inresults:metrics=results['test']metric_names=['MAE','MSE','R2']metric_values=[metrics[name]fornameinmetric_namesifnameinmetrics]metric_labels=[namefornameinmetric_namesifnameinmetrics]bars=ax6.bar(metric_labels,metric_values,color=model_color,alpha=0.8)ax6.set_title(f'{model_name}-TestSetPerformanceMetrics')ax6.set_ylabel('MetricValues')#添加数值标签forbar,valueinzip(bars,metric_values):height=bar.get_height()ax6.text(bar.get_x()+bar.get_width()/2.,height,f'{value:.4f}',ha='center',va='bottom',fontsize=9)plt.suptitle(f'{model_name}ModelCompleteAnalysis',fontsize=16,fontweight='bold')save_file=os.path.join(save_dir,f'{model_name}_Model_Complete_Analysis.png')plt.savefig(save_file,dpi=300,bbox_inches='tight')plt.close()print(f"{model_name}modelcompleteanalysissaved:{os.path.basename(save_file)}")returnsave_filedef_plot_single_model_analysis_separated(self,model_name:str,results:Dict,history:Dict,save_dir:str,experiment_name:str)->List[str]:"""分离式单个模型分析图表"""saved_files=[]model_color=self._get_high_contrast_color(model_name,0)#1.训练历史图ifhistory:plt.figure(figsize=(14,8))#检查训练历史的键名格式train_loss_key='train_loss'if'train_loss'inhistoryelse'loss'val_loss_key='val_loss'if'val_loss'inhistoryelse'val_loss'train_mae_key='train_mae'if'train_mae'inhistoryelse'mae'val_mae_key='val_mae'if'val_mae'inhistoryelse'val_mae'iftrain_loss_keyinhistory:epochs=range(1,len(history[train_loss_key])+1)plt.subplot(1,2,1)plt.plot(epochs,history[train_loss_key],label='TrainingLoss',color=model_color,linewidth=2.5)ifval_loss_keyinhistory:plt.plot(epochs,history[val_loss_key],label='ValidationLoss',color=model_color,linewidth=2.5,linestyle='--',alpha=0.8)plt.title(f'{model_name}-LossFunction')plt.xlabel('Epochs')plt.ylabel('Loss')plt.legend()plt.grid(True,alpha=0.3)plt.subplot(1,2,2)iftrain_mae_keyinhistory:plt.plot(epochs,history[train_mae_key],label='TrainingMAE',color=model_color,linewidth=2.5)ifval_mae_keyinhistory:plt.plot(epochs,history[val_mae_key],label='ValidationMAE',color=model_color,linewidth=2.5,linestyle='--',alpha=0.8)plt.title(f'{model_name}-MAEMetrics')plt.xlabel('Epochs')plt.ylabel('MAE')plt.legend()plt.grid(True,alpha=0.3)plt.suptitle(f'{model_name}-TrainingHistory',fontsize=16,fontweight='bold')plt.tight_layout()history_file=os.path.join(save_dir,f'{model_name}_Training_History.png')plt.savefig(history_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(history_file)print(f"{model_name}训练历史图已保存:{os.path.basename(history_file)}")#2.时间序列预测图if'predictions'inresultsand'test'inresults['predictions']:y_true,y_pred=results['predictions']['test']display_points=min(300,len(y_true))time_steps=range(display_points)plt.figure(figsize=(16,8))plt.plot(time_steps,y_true[:display_points],label='TrueValues',color='#212121',linewidth=2.5,alpha=1.0)plt.plot(time_steps,y_pred[:display_points],label=f'{model_name}Predictions',color=model_color,linewidth=2.5,alpha=0.9)plt.title(f'{model_name}-TimeSeriesPrediction',fontsize=16,fontweight='bold')plt.xlabel('TimeSteps',fontsize=12)plt.ylabel('Power(Normalized)',fontsize=12)plt.legend(fontsize=12)plt.grid(True,alpha=0.3)plt.tight_layout()timeseries_file=os.path.join(save_dir,f'{model_name}_Time_Series_Prediction.png')plt.savefig(timeseries_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(timeseries_file)print(f"{model_name}时间序列预测图已保存:{os.path.basename(timeseries_file)}")#3.性能指标图if'test'inresults:metrics=results['test']metric_names=['MAE','MSE','RMSE','R^2']metric_values=[metrics[name]fornameinmetric_namesifnameinmetrics]metric_labels=[namefornameinmetric_namesifnameinmetrics]plt.figure(figsize=(10,8))bars=plt.bar(metric_labels,metric_values,color=model_color,alpha=0.8,edgecolor='white',linewidth=2)plt.title(f'{model_name}-PerformanceMetrics',fontsize=16,fontweight='bold')plt.ylabel('MetricValues',fontsize=12)#添加数值标签forbar,valueinzip(bars,metric_values):height=bar.get_height()plt.text(bar.get_x()+bar.get_width()/2.,height,f'{value:.4f}',ha='center',va='bottom',fontsize=11,fontweight='bold')plt.grid(True,alpha=0.3,axis='y')plt.tight_layout()metrics_file=os.path.join(save_dir,f'{model_name}_Performance_Metrics.png')plt.savefig(metrics_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(metrics_file)print(f"{model_name}性能指标图已保存:{os.path.basename(metrics_file)}")returnsaved_filesdef_plot_data_distribution_universal(self,results:Dict,save_dir:str,experiment_name:str)->str:"""通用数据分布对比图"""dataset='test'model_names=[namefornameinresults.keys()if'predictions'inresults[name]anddatasetinresults[name]['predictions']]ifnotmodel_names:print(f"没有找到测试集预测结果，跳过数据分布图")return""fig,axes=plt.subplots(2,2,figsize=(16,12))#获取第一个模型的真实值y_true,_=results[model_names[0]]['predictions'][dataset]#1.真实值分布ax1=axes[0,0]ax1.hist(y_true,bins=50,alpha=0.7,color='#212121',edgecolor='white')ax1.set_title('TrueValuesDistribution',fontsize=12,fontweight='bold')ax1.set_xlabel('PowerValues')ax1.set_ylabel('Frequency')ax1.grid(True,alpha=0.3)#2.预测值分布对比ax2=axes[0,1]formodel_nameinmodel_names:_,y_pred=results[model_name]['predictions'][dataset]color=self._get_model_color(model_name)ax2.hist(y_pred,bins=50,alpha=0.6,color=color,edgecolor='white',label=f'{model_name}Predictions')ax2.hist(y_true,bins=50,alpha=0.8,color='#212121',edgecolor='white',label='TrueValues',linewidth=2)ax2.set_title('PredictedValuesDistributionComparison',fontsize=12,fontweight='bold')ax2.set_xlabel('PowerValues')ax2.set_ylabel('Frequency')ax2.legend()ax2.grid(True,alpha=0.3)#3.Q-Q图对比ax3=axes[1,0]fromscipyimportstatsformodel_nameinmodel_names:_,y_pred=results[model_name]['predictions'][dataset]color=self._get_model_color(model_name)stats.probplot(y_pred,dist="norm",plot=ax3)ax3.get_lines()[-1].set_color(color)ax3.get_lines()[-1].set_label(f'{model_name}')ax3.set_title('Q-QPlot(NormalityTest)',fontsize=12,fontweight='bold')ax3.legend()ax3.grid(True,alpha=0.3)#4.残差分布对比ax4=axes[1,1]formodel_nameinmodel_names:_,y_pred=results[model_name]['predictions'][dataset]residuals=y_pred-y_truecolor=self._get_model_color(model_name)ax4.hist(residuals,bins=30,alpha=0.6,color=color,edgecolor='white',label=f'{model_name}Residuals')ax4.axvline(x=0,color='red',linestyle='--',linewidth=2)ax4.set_title('ResidualDistributionComparison',fontsize=12,fontweight='bold')ax4.set_xlabel('ResidualValues')ax4.set_ylabel('Frequency')ax4.legend()ax4.grid(True,alpha=0.3)plt.suptitle('DataDistributionAnalysis',fontsize=16,fontweight='bold')plt.tight_layout()save_file=os.path.join(save_dir,'Data_Distribution_Analysis.png')plt.savefig(save_file,dpi=300,bbox_inches='tight')plt.close()print(f"数据分布分析图已保存:{os.path.basename(save_file)}")returnsave_filedef_plot_data_distribution_separated(self,results:Dict,save_dir:str,experiment_name:str)->List[str]:"""分离式数据分布分析图表"""saved_files=[]dataset='test'model_names=[namefornameinresults.keys()if'predictions'inresults[name]anddatasetinresults[name]['predictions']]ifnotmodel_names:returnsaved_files#获取第一个模型的真实值y_true,_=results[model_names[0]]['predictions'][dataset]#1.真实值分布图plt.figure(figsize=(12,8))plt.hist(y_true,bins=50,alpha=0.7,color=self.high_contrast_colors['primary'],edgecolor='white',linewidth=1.5)plt.title('TrueValuesDistribution',fontsize=16,fontweight='bold')plt.xlabel('PowerValues',fontsize=12)plt.ylabel('Frequency',fontsize=12)plt.grid(True,alpha=0.3)#添加统计信息mean_true=np.mean(y_true)std_true=np.std(y_true)plt.text(0.05,0.95,f'均值:{mean_true:.4f}\n标准差:{std_true:.4f}',transform=plt.gca().transAxes,bbox=dict(boxstyle='round',facecolor='white',alpha=0.8),fontsize=12,verticalalignment='top',fontweight='bold')plt.tight_layout()true_dist_file=os.path.join(save_dir,'True_Values_Distribution.png')plt.savefig(true_dist_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(true_dist_file)print(f"真实值分布图已保存:{os.path.basename(true_dist_file)}")#2.各模型预测值分布对比plt.figure(figsize=(14,8))fori,model_nameinenumerate(model_names):_,y_pred=results[model_name]['predictions'][dataset]model_color=self._get_high_contrast_color(model_name,i)plt.hist(y_pred,bins=50,alpha=0.6,label=f'{model_name}Predictions',color=model_color,edgecolor='white',linewidth=1)#添加真实值分布作为参考plt.hist(y_true,bins=50,alpha=0.8,label='TrueValues',color=self.high_contrast_colors['primary'],edgecolor='white',linewidth=1.5)plt.title('PredictedValuesDistributionComparison',fontsize=16,fontweight='bold')plt.xlabel('PowerValues',fontsize=12)plt.ylabel('Frequency',fontsize=12)plt.legend(fontsize=11)plt.grid(True,alpha=0.3)plt.tight_layout()pred_dist_file=os.path.join(save_dir,'Predicted_Values_Distribution_Comparison.png')plt.savefig(pred_dist_file,dpi=300,bbox_inches='tight')plt.close()saved_files.append(pred_dist_file)print(f"预测值分布对比图已保存:{os.path.basename(pred_dist_file)}")returnsaved_filesdef_plot_relative_percentage_error_with_inset(self,results:Dict,dataset:str,save_dir:str,experiment_name:str)->str:"""绘制相对百分比误差时间序列图（带插图）模仿论文中的可视化风格Args:results:模型结果字典dataset:数据集名称save_dir:保存目录experiment_name:实验名称Returns:保存的图片文件路径"""#获取有预测结果的模型available_models=[namefornameinresults.keys()if'predictions'inresults[name]anddatasetinresults[name]['predictions']]#根据配置过滤模型model_names=self._filter_models_by_config(available_models)ifnotmodel_names:print(f"没有找到启用的{dataset}集预测结果，跳过相对百分比误差图")return""print(f"根据配置显示模型:{','.join(model_names)}")#设置学术风格setup_matplotlib()#创建图形fig,ax=plt.subplots(figsize=(12,8))#定义线型和标记样式line_styles=['-','--','-.',':','-','--']markers=['o','s','^','D','v','p']#获取数据并计算相对百分比误差display_points=min(400,len(results[model_names[0]]['predictions'][dataset][0]))time_steps=range(display_points)#存储所有模型的相对百分比误差all_errors={}fori,model_nameinenumerate(model_names):y_true,y_pred=results[model_name]['predictions'][dataset]#计算相对百分比误差，避免除零withnp.errstate(divide='ignore',invalid='ignore'):relative_error=((y_pred[:display_points]-y_true[:display_points])/np.where(y_true[:display_points]!=0,y_true[:display_points],1))*100#处理无穷大和NaN值relative_error=np.where(np.isfinite(relative_error),relative_error,0)all_errors[model_name]=relative_error#获取颜色color=self._get_high_contrast_color(model_name,i)#绘制主图ax.plot(time_steps,relative_error,label=model_name,color=color,linewidth=2,linestyle=line_styles[i%len(line_styles)],alpha=0.8)#设置主图样式ax.set_xlabel('Time',fontsize=14)ax.set_ylabel('Relativepercentageerror',fontsize=14)ax.set_title(f'RelativePercentageErrorComparison-{experiment_name}',fontsize=16,pad=20)ax.grid(True,alpha=0.3)ax.legend(loc='upperright',fontsize=10)#设置Y轴范围，确保显示效果好all_values=np.concatenate(list(all_errors.values()))y_min,y_max=np.percentile(all_values,[5,95])y_range=y_max-y_minax.set_ylim(y_min-0.1*y_range,y_max+0.1*y_range)#添加插图（inset）-显示局部细节#选择一个有趣的区间进行放大inset_start=int(display_points*0.7)#从70%位置开始inset_end=int(display_points*0.9)#到90%位置结束#创建插图axins=inset_axes(ax,width="40%",height="40%",loc='upperleft',bbox_to_anchor=(0.05,0.55,0.4,0.4),bbox_transform=ax.transAxes)#在插图中绘制放大的区域inset_time=range(inset_start,inset_end)fori,model_nameinenumerate(model_names):color=self._get_high_contrast_color(model_name,i)axins.plot(inset_time,all_errors[model_name][inset_start:inset_end],color=color,linewidth=1.5,linestyle=line_styles[i%len(line_styles)],marker=markers[i%len(markers)],markersize=3,alpha=0.9)#设置插图样式axins.grid(True,alpha=0.3)axins.tick_params(labelsize=8)#在主图上标记插图区域ax.axvspan(inset_start,inset_end,alpha=0.2,color='gray',linestyle='--')#添加箭头指向插图区域frommatplotlib.patchesimportConnectionPatchcon1=ConnectionPatch(xyA=(inset_start,ax.get_ylim()[1]),xyB=(0,1),coordsA='data',coordsB='axesfraction',axesA=ax,axesB=axins,arrowstyle='->',color='gray',alpha=0.7)ax.add_patch(con1)con2=ConnectionPatch(xyA=(inset_end,ax.get_ylim()[1]),xyB=(1,1),coordsA='data',coordsB='axesfraction',axesA=ax,axesB=axins,arrowstyle='->',color='gray',alpha=0.7)ax.add_patch(con2)#保存图片filename=f"{experiment_name}_relative_percentage_error_with_inset_{dataset}.png"filepath=os.path.join(save_dir,filename)plt.tight_layout()plt.savefig(filepath,dpi=300,bbox_inches='tight')plt.close()print(f"相对百分比误差图（带插图）已保存:{os.path.basename(filepath)}")returnfilepathdef_plot_relative_percentage_error_simple(self,results:Dict,dataset:str,save_dir:str,experiment_name:str)->str:"""绘制简化版相对百分比误差时间序列图（无插图）时间步长为100，适合快速对比Args:results:模型结果字典dataset:数据集名称save_dir:保存目录experiment_name:实验名称Returns:保存的图片文件路径"""#获取有预测结果的模型available_models=[namefornameinresults.keys()if'predictions'inresults[name]anddatasetinresults[name]['predictions']]#根据配置过滤模型model_names=self._filter_models_by_config(available_models)ifnotmodel_names:print(f"没有找到启用的{dataset}集预测结果，跳过相对百分比误差图")return""print(f"根据配置显示模型:{','.join(model_names)}")#设置学术风格setup_matplotlib()#创建图形-简化版本，无插图fig,ax=plt.subplots(figsize=(12,6))#定义线型和标记样式line_styles=['-','--','-.',':','-','--']#获取数据并计算相对百分比误差-缩减至100个时间步display_points=min(100,len(results[model_names[0]]['predictions'][dataset][0]))time_steps=range(display_points)#存储所有模型的相对百分比误差all_errors={}fori,model_nameinenumerate(model_names):y_true,y_pred=results[model_name]['predictions'][dataset]#计算相对百分比误差，避免除零withnp.errstate(divide='ignore',invalid='ignore'):relative_error=((y_pred[:display_points]-y_true[:display_points])/np.where(y_true[:display_points]!=0,y_true[:display_points],1))*100#处理无穷大和NaN值relative_error=np.where(np.isfinite(relative_error),relative_error,0)all_errors[model_name]=relative_error#获取颜色color=self._get_high_contrast_color(model_name,i)#绘制主图ax.plot(time_steps,relative_error,label=model_name,color=color,linewidth=2.5,linestyle=line_styles[i%len(line_styles)],alpha=0.9)#设置图表样式ax.set_xlabel('Time',fontsize=14,fontweight='bold')ax.set_ylabel('Relativepercentageerror(%)',fontsize=14,fontweight='bold')ax.set_title(f'RelativePercentageErrorComparison-{experiment_name}',fontsize=16,fontweight='bold',pad=20)ax.grid(True,alpha=0.3)ax.legend(loc='best',fontsize=11,frameon=True,fancybox=True,shadow=True)#设置Y轴范围，确保显示效果好all_values=np.concatenate(list(all_errors.values()))y_min,y_max=np.percentile(all_values,[5,95])y_range=y_max-y_minax.set_ylim(y_min-0.1*y_range,y_max+0.1*y_range)#设置X轴范围ax.set_xlim(0,display_points-1)#美化图表ax.spines['top'].set_visible(False)ax.spines['right'].set_visible(False)ax.spines['left'].set_linewidth(1.2)ax.spines['bottom'].set_linewidth(1.2)#保存图片filename=f"{experiment_name}_relative_percentage_error_{dataset}.png"filepath=os.path.join(save_dir,filename)plt.tight_layout()plt.savefig(filepath,dpi=300,bbox_inches='tight',facecolor='white')plt.close()print(f"简化版相对百分比误差图已保存:{os.path.basename(filepath)}")returnfilepathdefquick_visualize(self,models_data:Dict[str,Tuple[Dict,Dict]],experiment_name:str="Quick_Analysis",save_path:Optional[str]=None)->List[str]:"""快速可视化接口-简化的使用方式Args:models_data:{model_name:(results,history)}格式的数据experiment_name:实验名称save_path:保存路径Returns:生成的图片文件路径列表"""#转换数据格式results={}histories={}formodel_name,(model_results,model_history)inmodels_data.items():results[model_name]=model_resultsifmodel_historyisnotNone:histories[model_name]=model_historyreturnself.generate_complete_visualization_suite(results=results,histories=histories,save_path=save_path,experiment_name=experiment_name)#便捷函数defcreate_universal_visualizations(models_data:Dict[str,Tuple[Dict,Dict]],experiment_name:str="Model_Comparison",save_path:Optional[str]=None)->List[str]:"""创建通用可视化的便捷函数Args:models_data:{model_name:(results,history)}格式的数据experiment_name:实验名称save_path:保存路径Returns:生成的图片文件路径列表Example:#使用示例models_data={'BiGRU':(bigru_results,bigru_history),'DPTAM-BiGRU':(dptam_results,dptam_history)}files=create_universal_visualizations(models_data,"BiGRU_vs_DPTAM")"""visualizer=UniversalVisualizer()returnvisualizer.quick_visualize(models_data,experiment_name,save_path)#测试代码if__name__=="__main__":print("通用可视化系统测试")print("="*50)#创建可视化工具实例visualizer=UniversalVisualizer()#测试颜色配置print("模型颜色映射:")test_models=['GRU','LSTM','BiGRU','DPTAM-BiGRU','CustomModel']formodelintest_models:color=visualizer._get_model_color(model)print(f"{model}:{color}")print("\n通用可视化系统初始化完成")print("使用create_universal_visualizations()函数快速生成可视化")