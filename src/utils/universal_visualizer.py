"""
通用可视化系统
支持任意模型组合的标准化可视化流程
"""

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Union
import os
import sys
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from mpl_toolkits.axes_grid1.inset_locator import inset_axes

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import ACADEMIC_COLORS, MODEL_COLORS, AUTO_COLOR_POOL, get_current_paths, setup_matplotlib, COMPARISON_EXPERIMENT_CONFIG

# 优雅浅色方案 - 真实值浅灰色虚线，模型用浅色实线
HIGH_CONTRAST_COLORS = {
    'primary': '#666666',      # 浅灰色 - 真实值（虚线）
    'model1': '#FF9999',       # 浅红色 - Baseline-BiGRU（实线）
    'model2': '#9999FF',       # 浅蓝色 - DPTAM-BiGRU（实线）
    'model3': '#FFCC66',       # 浅黄色 - ASB-DPTAM-BiGRU（实线）
    'model4': '#FF9966',       # 浅橙色 - 备用
    'model5': '#CC99CC',       # 浅紫色 - 备用
}

# 线条样式配置 - 真实值虚线，模型实线，更细的线条
LINE_STYLES = ['--', '-', '-', '-', '-', '-', '-', '-', '-', '-']  # 真实值虚线，其他实线
LINE_WIDTHS = [2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0]  # 更细的线宽，支持更多模型

class UniversalVisualizer:
    """通用可视化系统 - 支持任意模型组合的标准化可视化"""
    
    def __init__(self):
        """初始化通用可视化工具"""
        setup_matplotlib()
        self._setup_enhanced_font()  # 增强字体设置
        self.colors = ACADEMIC_COLORS
        self.model_colors = MODEL_COLORS.copy()
        self.auto_color_pool = AUTO_COLOR_POOL.copy()
        self.used_auto_colors = set()
        self.high_contrast_colors = HIGH_CONTRAST_COLORS

    def _setup_enhanced_font(self):
        """增强字体设置，解决方框字问题"""
        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            plt.rcParams['font.size'] = 12
            plt.rcParams['axes.titlesize'] = 16
            plt.rcParams['axes.labelsize'] = 12
            plt.rcParams['xtick.labelsize'] = 10
            plt.rcParams['ytick.labelsize'] = 10
            plt.rcParams['legend.fontsize'] = 11
            print("✅ 增强字体设置完成")
        except Exception as e:
            print(f"⚠️ 字体设置失败: {e}")

    def _get_high_contrast_color(self, model_name: str, index: int = 0) -> str:
        """获取高对比度颜色"""
        # 精确匹配模型名称
        if 'Baseline-BiGRU' in model_name or model_name == 'Baseline-BiGRU':
            return self.high_contrast_colors['model1']  # 亮红色
        elif 'DPTAM-BiGRU' in model_name and 'ASB' not in model_name:
            return self.high_contrast_colors['model2']  # 亮蓝色
        elif 'ASB-DPTAM-BiGRU' in model_name or 'ASB' in model_name:
            return self.high_contrast_colors['model3']  # 亮绿色
        else:
            # 按顺序分配颜色
            color_keys = ['model1', 'model2', 'model3', 'model4', 'model5']
            return self.high_contrast_colors.get(color_keys[index % len(color_keys)], '#000000')
        
    def _get_model_color(self, model_name: str) -> str:
        """获取模型颜色，支持自动分配"""
        if model_name in self.model_colors:
            return self.model_colors[model_name]
        
        # 自动分配颜色
        available_colors = [c for c in self.auto_color_pool if c not in self.used_auto_colors]
        if available_colors:
            color = available_colors[0]
            self.model_colors[model_name] = color
            self.used_auto_colors.add(color)
            return color
        else:
            # 如果颜色池用完，使用默认颜色
            return self.colors['primary']

    def _filter_models_by_config(self, model_names: List[str]) -> List[str]:
        """
        根据配置文件中的模型开关过滤模型列表

        Args:
            model_names: 原始模型名称列表

        Returns:
            过滤后的模型名称列表
        """
        # 模型名称到配置键的映射
        model_config_mapping = {
            'BiGRU': 'enable_baseline_bigru',
            'DPTAM-BiGRU': 'enable_dptam_bigru',
            'ASB-DPTAM-BiGRU': 'enable_asb_dptam_bigru',
            'BiLSTM': 'enable_bilstm',
            'CNN-BiLSTM': 'enable_cnn_bilstm',
            'CNN-BiLSTM-Attention': 'enable_cnn_bilstm_attention',
            'CNNBiLSTMAttentionModel': 'enable_cnn_bilstm_attention',
            'CNN-BiGRU': 'enable_cnn_bigru',
            'CNN-BiGRU-Attention': 'enable_cnn_bigru_attention',
            'CNNBiGRUAttentionModel': 'enable_cnn_bigru_attention'
        }

        filtered_models = []

        for model_name in model_names:
            # 查找对应的配置键
            config_key = model_config_mapping.get(model_name)

            if config_key is None:
                # 如果没有找到对应的配置键，默认包含该模型
                print(f"⚠️ 模型 '{model_name}' 没有对应的配置开关，默认包含")
                filtered_models.append(model_name)
            else:
                # 检查配置开关
                if COMPARISON_EXPERIMENT_CONFIG.get(config_key, False):
                    filtered_models.append(model_name)
                else:
                    print(f"🚫 模型 '{model_name}' 被配置开关 '{config_key}' 禁用，跳过")

        return filtered_models
    
    def generate_complete_visualization_suite(self, 
                                            results: Dict[str, Dict], 
                                            histories: Dict[str, Dict],
                                            save_path: Optional[str] = None,
                                            experiment_name: str = "Model_Comparison") -> List[str]:
        """
        生成完整的可视化套件
        
        Args:
            results: 模型结果字典 {model_name: {dataset: metrics, predictions: {dataset: (y_true, y_pred)}}}
            histories: 训练历史字典 {model_name: {train_loss: [], val_loss: [], ...}}
            save_path: 保存路径
            experiment_name: 实验名称
            
        Returns:
            生成的图片文件路径列表
        """
        if save_path is None:
            save_path = get_current_paths()['figures']

        # 创建分类子目录
        experiment_dir = save_path
        os.makedirs(experiment_dir, exist_ok=True)

        # 创建分类文件夹
        categories = {
            'training_analysis': '训练分析',
            'prediction_comparison': '预测对比',
            'performance_metrics': '性能指标',
            'error_analysis': 'Error Analysis',
            'model_analysis': '模型分析',
            'data_distribution': '数据分布'
        }

        category_dirs = {}
        for category, chinese_name in categories.items():
            category_dir = os.path.join(experiment_dir, f"{category}_{chinese_name}")
            os.makedirs(category_dir, exist_ok=True)
            category_dirs[category] = category_dir

        saved_files = []
        model_names = list(results.keys())

        print(f"\n🎨 开始生成 {experiment_name} 完整可视化套件...")
        print(f"📁 保存目录: {experiment_dir}")
        print(f"🔍 检测到模型: {', '.join(model_names)}")
        
        try:
            # 1. 训练历史对比图 - 分解为单独图表
            if histories:
                training_files = self._plot_training_history_separated(histories, category_dirs['training_analysis'], experiment_name)
                saved_files.extend(training_files)

            # 2. 预测结果对比图 - 分解为单独图表
            if self._check_dataset_exists(results, 'test'):
                prediction_files = self._plot_prediction_comparison_separated(results, 'test', category_dirs['prediction_comparison'], experiment_name)
                saved_files.extend(prediction_files)

            # 3. 性能指标对比图 - 分解为单独图表
            metrics_files = self._plot_metrics_comparison_separated(results, category_dirs['performance_metrics'], experiment_name)
            saved_files.extend(metrics_files)

            # 4. 误差分析图 - 分解为单独图表
            error_files = self._plot_error_analysis_separated(results, category_dirs['error_analysis'], experiment_name)
            saved_files.extend(error_files)

            # 5. 数据分布分析 - 分解为单独图表
            distribution_files = self._plot_data_distribution_separated(results, category_dirs['data_distribution'], experiment_name)
            saved_files.extend(distribution_files)

            # 6. 相对百分比误差时间序列图（简化版，100时间步，无插图）
            if self._check_dataset_exists(results, 'test'):
                relative_error_file = self._plot_relative_percentage_error_simple(results, 'test', category_dirs['error_analysis'], experiment_name)
                if relative_error_file:
                    saved_files.append(relative_error_file)

            # 7. 单个模型详细分析图 - 分解为单独图表
            for model_name in model_names:
                if model_name in histories:
                    model_files = self._plot_single_model_analysis_separated(
                        model_name, results[model_name], histories[model_name],
                        category_dirs['model_analysis'], experiment_name
                    )
                    saved_files.extend(model_files)
            
            print(f"\n🎉 {experiment_name} 可视化套件生成完成！")
            print(f"📊 共生成 {len(saved_files)} 个图表文件")
            print("📋 生成的图表:")
            for i, file_path in enumerate(saved_files, 1):
                filename = os.path.basename(file_path)
                print(f"  {i:2d}. {filename}")
            
            return saved_files
            
        except Exception as e:
            print(f"❌ 生成可视化套件时出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return saved_files
    
    def _check_dataset_exists(self, results: Dict, dataset: str) -> bool:
        """检查数据集是否存在于所有模型中"""
        for model_name, model_results in results.items():
            if 'predictions' in model_results and dataset in model_results['predictions']:
                return True
        return False
    
    def _plot_training_history_universal(self, histories: Dict, save_dir: str, experiment_name: str) -> str:
        """通用训练历史对比图"""
        fig, axes = plt.subplots(1, 2, figsize=(16, 6))
        
        # 损失函数对比
        ax1 = axes[0]
        for model_name, history in histories.items():
            color = self._get_model_color(model_name)
            
            if isinstance(history, dict):
                epochs = range(1, len(history['train_loss']) + 1)
                train_loss = history['train_loss']
                val_loss = history['val_loss']
            else:
                epochs = range(1, len(history.history['loss']) + 1)
                train_loss = history.history['loss']
                val_loss = history.history['val_loss']
            
            ax1.plot(epochs, train_loss, label=f'{model_name} Training Loss',
                    color=color, linewidth=2.5, alpha=0.8)
            ax1.plot(epochs, val_loss, label=f'{model_name} Validation Loss',
                    color=color, linewidth=2.5, linestyle='--', alpha=0.8)
        
        ax1.set_title('Model Training Loss Comparison', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Epochs')
        ax1.set_ylabel('Loss (MSE)')
        ax1.legend(frameon=True, fancybox=True, shadow=True)
        ax1.grid(True, alpha=0.3)
        
        # MAE对比
        ax2 = axes[1]
        for model_name, history in histories.items():
            color = self._get_model_color(model_name)
            
            if isinstance(history, dict):
                epochs = range(1, len(history['train_mae']) + 1)
                train_mae = history['train_mae']
                val_mae = history['val_mae']
            else:
                epochs = range(1, len(history.history['mae']) + 1)
                train_mae = history.history['mae']
                val_mae = history.history['val_mae']
            
            ax2.plot(epochs, train_mae, label=f'{model_name} Training MAE',
                    color=color, linewidth=2.5, alpha=0.8)
            ax2.plot(epochs, val_mae, label=f'{model_name} Validation MAE',
                    color=color, linewidth=2.5, linestyle='--', alpha=0.8)
        
        ax2.set_title('Model Training MAE Comparison', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Epochs')
        ax2.set_ylabel('Mean Absolute Error (MAE)')
        ax2.legend(frameon=True, fancybox=True, shadow=True)
        ax2.grid(True, alpha=0.3)
        
        plt.suptitle('Training History Comparison', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        save_file = os.path.join(save_dir, 'Training_History_Comparison.png')
        plt.savefig(save_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 训练历史对比图已保存: {os.path.basename(save_file)}")
        return save_file

    def _plot_training_history_separated(self, histories: Dict, save_dir: str, experiment_name: str) -> List[str]:
        """分离式训练历史图表"""
        saved_files = []

        # 1. 损失函数对比图
        plt.figure(figsize=(12, 8))
        for i, (model_name, history) in enumerate(histories.items()):
            # 检查训练历史的键名格式
            train_loss_key = 'train_loss' if 'train_loss' in history else 'loss'
            val_loss_key = 'val_loss' if 'val_loss' in history else 'val_loss'

            if train_loss_key in history:
                epochs = range(1, len(history[train_loss_key]) + 1)
                model_color = self._get_high_contrast_color(model_name, i)
                plt.plot(epochs, history[train_loss_key],
                        label=f'{model_name} - Training Loss', linewidth=LINE_WIDTHS[min(i+1, len(LINE_WIDTHS)-1)],
                        alpha=1.0, color=model_color, linestyle='-')  # 实线
                if val_loss_key in history:
                    plt.plot(epochs, history[val_loss_key],
                            label=f'{model_name} - Validation Loss', linewidth=LINE_WIDTHS[min(i+1, len(LINE_WIDTHS)-1)],
                            alpha=1.0, color=model_color, linestyle='--')  # 虚线

        plt.title('Training Loss Comparison', fontsize=16, fontweight='bold')
        plt.xlabel('Epochs', fontsize=12)
        plt.ylabel('Loss', fontsize=12)
        plt.legend(fontsize=10)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        loss_file = os.path.join(save_dir, 'Training_Loss_Comparison.png')
        plt.savefig(loss_file, dpi=300, bbox_inches='tight')
        plt.close()
        saved_files.append(loss_file)
        print(f"✅ 训练损失对比图已保存: {os.path.basename(loss_file)}")

        # 2. MAE指标对比图
        plt.figure(figsize=(12, 8))
        for i, (model_name, history) in enumerate(histories.items()):
            # 检查MAE历史的键名格式
            train_mae_key = 'train_mae' if 'train_mae' in history else 'mae'
            val_mae_key = 'val_mae' if 'val_mae' in history else 'val_mae'

            if train_mae_key in history:
                epochs = range(1, len(history[train_mae_key]) + 1)
                model_color = self._get_high_contrast_color(model_name, i)
                plt.plot(epochs, history[train_mae_key],
                        label=f'{model_name} - Training MAE', linewidth=LINE_WIDTHS[i+1],
                        alpha=1.0, color=model_color, linestyle='-')  # 实线
                if val_mae_key in history:
                    plt.plot(epochs, history[val_mae_key],
                            label=f'{model_name} - Validation MAE', linewidth=LINE_WIDTHS[i+1],
                            alpha=1.0, color=model_color, linestyle='--')  # 虚线

        plt.title('MAE Metrics Comparison', fontsize=16, fontweight='bold')
        plt.xlabel('Epochs', fontsize=12)
        plt.ylabel('MAE', fontsize=12)
        plt.legend(fontsize=10)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        mae_file = os.path.join(save_dir, 'MAE_Metrics_Comparison.png')
        plt.savefig(mae_file, dpi=300, bbox_inches='tight')
        plt.close()
        saved_files.append(mae_file)
        print(f"✅ MAE指标对比图已保存: {os.path.basename(mae_file)}")

        return saved_files
    
    def _plot_prediction_comparison_universal(self, results: Dict, dataset: str, save_dir: str, experiment_name: str) -> str:
        """通用预测结果对比图"""
        model_names = [name for name in results.keys() 
                      if 'predictions' in results[name] and dataset in results[name]['predictions']]
        
        if not model_names:
            print(f"⚠️ 数据集 {dataset} 不存在预测结果，跳过")
            return ""
        
        n_models = len(model_names)
        fig, axes = plt.subplots(2, n_models, figsize=(6*n_models, 10))
        
        if n_models == 1:
            axes = axes.reshape(2, 1)
        
        # 第一行：散点图对比
        for i, model_name in enumerate(model_names):
            ax = axes[0, i] if n_models > 1 else axes[0]
            y_true, y_pred = results[model_name]['predictions'][dataset]
            
            # 随机采样显示
            sample_size = min(500, len(y_true))
            if len(y_true) > sample_size:
                indices = np.random.choice(len(y_true), sample_size, replace=False)
                y_true_sample = y_true[indices]
                y_pred_sample = y_pred[indices]
            else:
                y_true_sample = y_true
                y_pred_sample = y_pred
            
            color = self._get_model_color(model_name)
            ax.scatter(y_true_sample, y_pred_sample, alpha=0.6, s=20, 
                      color=color, edgecolors='white', linewidth=0.5)
            
            # 完美预测线
            min_val = min(y_true_sample.min(), y_pred_sample.min())
            max_val = max(y_true_sample.max(), y_pred_sample.max())
            ax.plot([min_val, max_val], [min_val, max_val], 
                   'r--', linewidth=2, alpha=0.8, label='Perfect Prediction')
            
            ax.set_xlabel('True Values (MW)')
            ax.set_ylabel('Predicted Values (MW)')
            ax.set_title(f'{model_name} - Predictions vs True Values')
            ax.legend()
            
            # 添加R^2值
            r2 = r2_score(y_true, y_pred)
            ax.text(0.05, 0.95, f'R^2 = {r2:.3f}', 
                   transform=ax.transAxes, 
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                   fontsize=10, fontweight='bold')
        
        # 第二行：时间序列对比
        display_points = min(300, min([len(results[name]['predictions'][dataset][0]) 
                                     for name in model_names]))
        
        for i, model_name in enumerate(model_names):
            ax = axes[1, i] if n_models > 1 else axes[1]
            y_true, y_pred = results[model_name]['predictions'][dataset]
            
            time_steps = range(display_points)
            ax.plot(time_steps, y_true[:display_points],
                   label='True Values', color='#212121', linewidth=2.5, alpha=1.0)
            
            color = self._get_model_color(model_name)
            ax.plot(time_steps, y_pred[:display_points],
                   label=f'{model_name} Predictions', color=color, linewidth=2.5, alpha=0.9)
            
            ax.set_title(f'{model_name} - Time Series Prediction Comparison')
            ax.set_xlabel('Time Steps')
            ax.set_ylabel('Power (MW)')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        plt.suptitle(f'{dataset.upper()} Set Prediction Results Comparison', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        save_file = os.path.join(save_dir, f'{dataset.upper()}_Set_Prediction_Results_Comparison.png')
        plt.savefig(save_file, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ {dataset} set prediction comparison saved: {os.path.basename(save_file)}")
        return save_file

    def _plot_prediction_comparison_separated(self, results: Dict, dataset: str, save_dir: str, experiment_name: str) -> List[str]:
        """分离式预测对比图表"""
        saved_files = []
        model_names = [name for name in results.keys()
                      if 'predictions' in results[name] and dataset in results[name]['predictions']]

        if not model_names:
            return saved_files

        # 获取第一个模型的真实值
        y_true, _ = results[model_names[0]]['predictions'][dataset]
        display_points = min(500, len(y_true))
        time_steps = range(display_points)

        # 1. 整体预测对比图
        plt.figure(figsize=(16, 10))
        plt.plot(time_steps, y_true[:display_points],
                label='True Values', color=self.high_contrast_colors['primary'],
                linewidth=LINE_WIDTHS[0], alpha=1.0, linestyle='--')  # 真实值固定为虚线

        for i, model_name in enumerate(model_names):
            _, y_pred = results[model_name]['predictions'][dataset]
            color = self._get_high_contrast_color(model_name, i)
            plt.plot(time_steps, y_pred[:display_points],
                    label=f'{model_name} Predictions',
                    color=color, linewidth=LINE_WIDTHS[min(i+1, len(LINE_WIDTHS)-1)], alpha=1.0,
                    linestyle='-')  # 模型预测值固定为实线

        plt.title(f'{dataset.upper()} Set Prediction Results Comparison', fontsize=16, fontweight='bold')
        plt.xlabel('Time Steps', fontsize=12)
        plt.ylabel('Power (Normalized)', fontsize=12)
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        overall_file = os.path.join(save_dir, f'{dataset.upper()}_Set_Overall_Prediction_Comparison.png')
        plt.savefig(overall_file, dpi=300, bbox_inches='tight')
        plt.close()
        saved_files.append(overall_file)
        print(f"✅ {dataset} set overall prediction comparison saved: {os.path.basename(overall_file)}")

        # 2. 局部细节对比图（前100个点）
        detail_points = min(100, len(y_true))
        detail_steps = range(detail_points)

        plt.figure(figsize=(16, 10))
        plt.plot(detail_steps, y_true[:detail_points],
                label='True Values', color=self.high_contrast_colors['primary'],
                linewidth=LINE_WIDTHS[0], alpha=1.0, marker='o', markersize=5,
                linestyle='--')  # 真实值固定为虚线

        markers = ['s', '^', 'D', 'v', 'p']
        for i, model_name in enumerate(model_names):
            _, y_pred = results[model_name]['predictions'][dataset]
            color = self._get_high_contrast_color(model_name, i)
            plt.plot(detail_steps, y_pred[:detail_points],
                    label=f'{model_name} Predictions',
                    color=color, linewidth=LINE_WIDTHS[min(i+1, len(LINE_WIDTHS)-1)], alpha=1.0,
                    marker=markers[i % len(markers)], markersize=4,
                    linestyle='-')  # 模型预测值固定为实线

        plt.title(f'{dataset.upper()} Set Prediction Details Comparison (First {detail_points} Time Steps)', fontsize=16, fontweight='bold')
        plt.xlabel('Time Steps', fontsize=12)
        plt.ylabel('Power (Normalized)', fontsize=12)
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        detail_file = os.path.join(save_dir, f'{dataset.upper()}_Set_Prediction_Details_Comparison.png')
        plt.savefig(detail_file, dpi=300, bbox_inches='tight')
        plt.close()
        saved_files.append(detail_file)
        print(f"✅ {dataset} set prediction details comparison saved: {os.path.basename(detail_file)}")

        return saved_files

    def _plot_combined_time_series_universal(self, results: Dict, save_dir: str, experiment_name: str) -> str:
        """通用综合时间序列对比图"""
        # 使用测试集数据
        dataset = 'test'
        model_names = [name for name in results.keys()
                      if 'predictions' in results[name] and dataset in results[name]['predictions']]

        if not model_names:
            print(f"⚠️ 没有找到测试集预测结果，跳过综合时间序列图")
            return ""

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 10))

        # 获取第一个模型的真实值（所有模型的真实值应该相同）
        y_true, _ = results[model_names[0]]['predictions'][dataset]
        display_points = min(300, len(y_true))
        time_steps = range(display_points)

        # 第一个子图：完整对比
        ax1.plot(time_steps, y_true[:display_points],
                label='Original Power', color='#212121', linewidth=3, alpha=1.0, zorder=len(model_names)+1)

        for i, model_name in enumerate(model_names):
            _, y_pred = results[model_name]['predictions'][dataset]
            color = self._get_model_color(model_name)
            ax1.plot(time_steps, y_pred[:display_points],
                    label=f'{model_name} Predictions', color=color,
                    linewidth=2.5, alpha=0.9, zorder=len(model_names)-i)

        ax1.set_title('Wind Power Prediction Comprehensive Comparison', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Time Steps')
        ax1.set_ylabel('Power (Normalized)')
        ax1.legend(frameon=True, fancybox=True, shadow=True, loc='upper right')
        ax1.grid(True, alpha=0.3)

        # 添加性能指标文本
        metrics_text = ""
        for model_name in model_names:
            _, y_pred = results[model_name]['predictions'][dataset]
            mae = mean_absolute_error(y_true, y_pred)
            mse = mean_squared_error(y_true, y_pred)
            r2 = r2_score(y_true, y_pred)
            metrics_text += f'{model_name}: MAE={mae:.4f}, MSE={mse:.4f}, R^2={r2:.3f}\n'

        ax1.text(0.02, 0.98, metrics_text.strip(),
                transform=ax1.transAxes,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.9),
                fontsize=10, verticalalignment='top', fontfamily='monospace')

        # 第二个子图：误差对比
        for model_name in model_names:
            _, y_pred = results[model_name]['predictions'][dataset]
            error = y_pred[:display_points] - y_true[:display_points]
            color = self._get_model_color(model_name)
            ax2.plot(time_steps, error, label=f'{model_name} Prediction Error',
                    color=color, linewidth=2, alpha=0.9)

        ax2.axhline(y=0, color='#FF5722', linestyle='--', linewidth=2.5, alpha=0.8)
        ax2.set_title('Prediction Error Comparison', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Time Steps')
        ax2.set_ylabel('Prediction Error (Normalized)')
        ax2.legend(frameon=True, fancybox=True, shadow=True)
        ax2.grid(True, alpha=0.3)

        # 添加误差统计信息
        error_text = "Mean Absolute Error:\n"
        for model_name in model_names:
            _, y_pred = results[model_name]['predictions'][dataset]
            error = y_pred[:display_points] - y_true[:display_points]
            error_mean = np.mean(np.abs(error))
            error_text += f'{model_name}: {error_mean:.4f}\n'

        ax2.text(0.98, 0.98, error_text.strip(),
                transform=ax2.transAxes,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.9),
                fontsize=10, verticalalignment='top', horizontalalignment='right',
                fontfamily='monospace')

        plt.suptitle('Comprehensive Time Series Prediction Comparison', fontsize=16, fontweight='bold')
        plt.tight_layout()

        save_file = os.path.join(save_dir, 'Comprehensive_Time_Series_Comparison.png')
        plt.savefig(save_file, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ 综合时间序列对比图已保存: {os.path.basename(save_file)}")
        return save_file

    def _plot_metrics_comparison_universal(self, results: Dict, save_dir: str, experiment_name: str) -> str:
        """通用评估指标对比图"""
        metrics = ['MAE', 'MSE', 'R2', 'MAPE']
        datasets = ['train', 'val', 'test']

        # 检查哪些数据集存在
        available_datasets = []
        for dataset in datasets:
            if any(dataset in results[model].get('predictions', {}) for model in results.keys()):
                available_datasets.append(dataset)

        if not available_datasets:
            print("⚠️ 没有找到可用的数据集，跳过指标对比图")
            return ""

        model_names = list(results.keys())
        n_metrics = len(metrics)

        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        axes = axes.flatten()

        x = np.arange(len(model_names))
        width = 0.25

        for i, metric in enumerate(metrics):
            ax = axes[i]

            for j, dataset in enumerate(available_datasets):
                values = []
                for model in model_names:
                    if dataset in results[model]:
                        values.append(results[model][dataset].get(metric, 0))
                    else:
                        values.append(0)

                # 选择颜色
                if dataset == 'train':
                    color = self.colors['primary']
                elif dataset == 'val':
                    color = self.colors['secondary']
                else:
                    color = self.colors['accent']

                bars = ax.bar(x + j * width, values, width,
                             label=f'{dataset.capitalize()} Set',
                             color=color, alpha=0.8, edgecolor='white')

                # 添加数值标签
                for bar, value in zip(bars, values):
                    if value > 0:  # 只显示有效值
                        height = bar.get_height()
                        ax.text(bar.get_x() + bar.get_width()/2., height,
                               f'{value:.3f}', ha='center', va='bottom', fontsize=8)

            ax.set_xlabel('Models')
            ax.set_ylabel(metric)
            ax.set_title(f'{metric} 对比')
            ax.set_xticks(x + width)
            ax.set_xticklabels(model_names, rotation=45, ha='right')
            ax.legend()
            ax.grid(True, alpha=0.3, axis='y')

        # 隐藏最后一个子图
        axes[-1].set_visible(False)

        plt.suptitle('Evaluation Metrics Comparison', fontsize=16, fontweight='bold')
        plt.tight_layout()

        save_file = os.path.join(save_dir, 'Evaluation_Metrics_Comparison.png')
        plt.savefig(save_file, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ 评估指标对比图已保存: {os.path.basename(save_file)}")
        return save_file

    def _plot_metrics_comparison_separated(self, results: Dict, save_dir: str, experiment_name: str) -> List[str]:
        """分离式性能指标对比图表"""
        saved_files = []
        model_names = list(results.keys())

        if not model_names:
            return saved_files

        # 准备数据
        metrics_data = {}
        for model_name in model_names:
            if 'test' in results[model_name]:
                metrics_data[model_name] = results[model_name]['test']

        if not metrics_data:
            return saved_files

        # 获取性能指标专用浅色
        bar_colors = []
        for i, model in enumerate(model_names):
            if 'Baseline-BiGRU' in model or model == 'Baseline-BiGRU':
                bar_colors.append('#FFB3B3')  # 浅红色
            elif 'DPTAM-BiGRU' in model and 'ASB' not in model:
                bar_colors.append('#B3D9FF')  # 浅蓝色
            elif 'ASB-DPTAM-BiGRU' in model or 'ASB' in model:
                bar_colors.append('#B3FFB3')  # 浅绿色
            else:
                bar_colors.append('#CCCCCC')  # 灰色备用

        # 1. RMSE对比图
        plt.figure(figsize=(12, 8))
        rmse_values = [metrics_data[model]['RMSE'] for model in model_names if model in metrics_data]
        bars = plt.bar(model_names, rmse_values, color=bar_colors, alpha=0.8, edgecolor='white', linewidth=2)
        plt.title('RMSE Performance Comparison', fontsize=16, fontweight='bold')
        plt.ylabel('RMSE值', fontsize=12)
        plt.xticks(rotation=45, ha='right')

        # 添加数值标签
        for bar, value in zip(bars, rmse_values):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.4f}', ha='center', va='bottom', fontsize=11, fontweight='bold')

        plt.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()

        rmse_file = os.path.join(save_dir, 'RMSE_Performance_Comparison.png')
        plt.savefig(rmse_file, dpi=300, bbox_inches='tight')
        plt.close()
        saved_files.append(rmse_file)
        print(f"✅ RMSE性能对比图已保存: {os.path.basename(rmse_file)}")

        # 2. R^2对比图
        plt.figure(figsize=(12, 8))
        r2_values = [metrics_data[model].get('R^2', metrics_data[model].get('R2', 0)) for model in model_names if model in metrics_data]
        bars = plt.bar(model_names, r2_values, color=bar_colors, alpha=0.8, edgecolor='white', linewidth=2)
        plt.title('R^2 Performance Comparison', fontsize=16, fontweight='bold')
        plt.ylabel('R^2 Value', fontsize=12)
        plt.xticks(rotation=45, ha='right')

        # 添加数值标签
        for bar, value in zip(bars, r2_values):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.4f}', ha='center', va='bottom', fontsize=11, fontweight='bold')

        plt.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()

        r2_file = os.path.join(save_dir, 'R2_Performance_Comparison.png')
        plt.savefig(r2_file, dpi=300, bbox_inches='tight')
        plt.close()
        saved_files.append(r2_file)
        print(f"✅ R^2性能对比图已保存: {os.path.basename(r2_file)}")

        # 3. MAE对比图
        plt.figure(figsize=(12, 8))
        mae_values = [metrics_data[model]['MAE'] for model in model_names if model in metrics_data]
        bars = plt.bar(model_names, mae_values, color=bar_colors, alpha=0.8, edgecolor='white', linewidth=2)
        plt.title('MAE Performance Comparison', fontsize=16, fontweight='bold')
        plt.ylabel('MAE', fontsize=12)
        plt.xticks(rotation=45, ha='right')

        # 添加数值标签
        for bar, value in zip(bars, mae_values):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.4f}', ha='center', va='bottom', fontsize=11, fontweight='bold')

        plt.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()

        mae_file = os.path.join(save_dir, 'MAE_Performance_Comparison.png')
        plt.savefig(mae_file, dpi=300, bbox_inches='tight')
        plt.close()
        saved_files.append(mae_file)
        print(f"✅ MAE性能对比图已保存: {os.path.basename(mae_file)}")

        return saved_files

    def _plot_error_analysis_universal(self, results: Dict, save_dir: str, experiment_name: str) -> str:
        """通用误差分析图"""
        dataset = 'test'
        model_names = [name for name in results.keys()
                      if 'predictions' in results[name] and dataset in results[name]['predictions']]

        if not model_names:
            print(f"⚠️ 没有找到测试集预测结果，跳过误差分析图")
            return ""

        n_models = len(model_names)
        fig, axes = plt.subplots(2, n_models, figsize=(6*n_models, 10))

        if n_models == 1:
            axes = axes.reshape(2, 1)

        for i, model_name in enumerate(model_names):
            y_true, y_pred = results[model_name]['predictions'][dataset]
            errors = y_pred - y_true
            color = self._get_model_color(model_name)

            # 误差分布直方图
            ax1 = axes[0, i] if n_models > 1 else axes[0]
            ax1.hist(errors, bins=50, alpha=0.7, color=color,
                    edgecolor='white', density=True)
            ax1.axvline(x=0, color='red', linestyle='--', linewidth=2)
            ax1.set_title(f'{model_name} - Error Distribution')
            ax1.set_xlabel('Prediction Error (MW)')
            ax1.set_ylabel('Density')
            ax1.grid(True, alpha=0.3)

            # 添加统计信息
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            ax1.text(0.05, 0.95, f'Mean: {mean_error:.3f}\nStd Dev: {std_error:.3f}',
                    transform=ax1.transAxes,
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                    fontsize=10, verticalalignment='top')

            # 误差 vs 真实值
            ax2 = axes[1, i] if n_models > 1 else axes[1]
            ax2.scatter(y_true, errors, alpha=0.6, s=15, color=color,
                       edgecolors='white', linewidth=0.5)
            ax2.axhline(y=0, color='red', linestyle='--', linewidth=2)
            ax2.set_title(f'{model_name} - Error vs True Values')
            ax2.set_xlabel('True Values (MW)')
            ax2.set_ylabel('Prediction Error (MW)')
            ax2.grid(True, alpha=0.3)

        plt.suptitle('Error Analysis', fontsize=16, fontweight='bold')
        plt.tight_layout()

        save_file = os.path.join(save_dir, 'Error_Analysis.png')
        plt.savefig(save_file, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ 误差分析图已保存: {os.path.basename(save_file)}")
        return save_file

    def _plot_error_analysis_separated(self, results: Dict, save_dir: str, experiment_name: str) -> List[str]:
        """分离式误差分析图表"""
        saved_files = []
        model_names = [name for name in results.keys()
                      if 'predictions' in results[name] and 'test' in results[name]['predictions']]

        if not model_names:
            return saved_files

        # 为每个模型生成单独的误差分析图
        for i, model_name in enumerate(model_names):
            y_true, y_pred = results[model_name]['predictions']['test']
            errors = y_pred - y_true
            model_color = self._get_high_contrast_color(model_name, i)

            # 1. 误差分布直方图
            plt.figure(figsize=(12, 8))
            plt.hist(errors, bins=50, alpha=0.7, color=model_color,
                    edgecolor='white', density=True, linewidth=1.5)
            plt.axvline(x=0, color='red', linestyle='--', linewidth=3, alpha=0.8)
            plt.title(f'{model_name} - Prediction Error Distribution', fontsize=16, fontweight='bold')
            plt.xlabel('Prediction Error', fontsize=12)
            plt.ylabel('Density', fontsize=12)
            plt.grid(True, alpha=0.3)

            # 添加统计信息
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            plt.text(0.05, 0.95, f'Mean: {mean_error:.4f}\nStd Dev: {std_error:.4f}',
                    transform=plt.gca().transAxes,
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                    fontsize=12, verticalalignment='top', fontweight='bold')

            plt.tight_layout()

            error_dist_file = os.path.join(save_dir, f'{model_name}_Error_Distribution.png')
            plt.savefig(error_dist_file, dpi=300, bbox_inches='tight')
            plt.close()
            saved_files.append(error_dist_file)
            print(f"✅ {model_name} error distribution saved: {os.path.basename(error_dist_file)}")

            # 2. 预测值vs真实值散点图
            plt.figure(figsize=(10, 10))
            plt.scatter(y_true, y_pred, alpha=0.6, color=model_color, s=20)

            # 添加完美预测线
            min_val = min(np.min(y_true), np.min(y_pred))
            max_val = max(np.max(y_true), np.max(y_pred))
            plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=3, alpha=0.8, label='Perfect Prediction Line')

            plt.title(f'{model_name} - Predictions vs True Values', fontsize=16, fontweight='bold')
            plt.xlabel('True Values', fontsize=12)
            plt.ylabel('Predicted Values', fontsize=12)
            plt.legend(fontsize=12)
            plt.grid(True, alpha=0.3)

            # 添加R^2值
            from sklearn.metrics import r2_score
            r2 = r2_score(y_true, y_pred)
            plt.text(0.05, 0.95, f'R^2 = {r2:.4f}',
                    transform=plt.gca().transAxes,
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                    fontsize=12, fontweight='bold')

            plt.tight_layout()

            scatter_file = os.path.join(save_dir, f'{model_name}_Prediction_Scatter_Plot.png')
            plt.savefig(scatter_file, dpi=300, bbox_inches='tight')
            plt.close()
            saved_files.append(scatter_file)
            print(f"✅ {model_name} prediction scatter plot saved: {os.path.basename(scatter_file)}")

        return saved_files

    def _plot_performance_radar_universal(self, results: Dict, save_dir: str, experiment_name: str) -> str:
        """通用性能雷达图"""
        model_names = list(results.keys())
        dataset = 'test'

        # 检查是否有测试集结果
        if not any(dataset in results[model] for model in model_names):
            print(f"⚠️ 没有找到测试集结果，跳过性能雷达图")
            return ""

        # 定义评估指标（需要标准化）
        metrics = ['MAE', 'MSE', 'R2', 'MAPE']
        available_metrics = []

        # 检查哪些指标可用
        for metric in metrics:
            if any(metric in results[model].get(dataset, {}) for model in model_names):
                available_metrics.append(metric)

        if len(available_metrics) < 3:
            print(f"⚠️ 可用指标不足，跳过性能雷达图")
            return ""

        # 准备数据
        angles = np.linspace(0, 2 * np.pi, len(available_metrics), endpoint=False).tolist()
        angles += angles[:1]  # 闭合雷达图

        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))

        for model_name in model_names:
            if dataset not in results[model_name]:
                continue

            values = []
            for metric in available_metrics:
                value = results[model_name][dataset].get(metric, 0)

                # 标准化处理（R2越大越好，其他指标越小越好）
                if metric == 'R2':
                    normalized_value = value  # R2已经在0-1之间
                else:
                    # 对于误差指标，转换为性能分数（1 - normalized_error）
                    max_val = max([results[m][dataset].get(metric, 0) for m in model_names
                                  if dataset in results[m]])
                    if max_val > 0:
                        normalized_value = 1 - (value / max_val)
                    else:
                        normalized_value = 0

                values.append(normalized_value)

            values += values[:1]  # 闭合雷达图

            color = self._get_model_color(model_name)
            ax.plot(angles, values, 'o-', linewidth=2, label=model_name, color=color)
            ax.fill(angles, values, alpha=0.25, color=color)

        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(available_metrics)
        ax.set_ylim(0, 1)
        ax.set_title('Model Performance Radar Chart', size=16, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)

        plt.tight_layout()

        save_file = os.path.join(save_dir, 'Performance_Radar_Chart.png')
        plt.savefig(save_file, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ Performance radar chart saved: {os.path.basename(save_file)}")
        return save_file

    def _plot_single_model_analysis_universal(self, model_name: str, results: Dict, history: Dict,
                                            save_dir: str, experiment_name: str) -> str:
        """通用单个模型详细分析图"""
        fig = plt.figure(figsize=(16, 12))
        gs = fig.add_gridspec(3, 2, height_ratios=[1, 1, 1], hspace=0.3, wspace=0.3)

        model_color = self._get_model_color(model_name)

        # 1. 训练历史 - 损失
        ax1 = fig.add_subplot(gs[0, 0])
        if isinstance(history, dict):
            epochs = range(1, len(history['train_loss']) + 1)
            ax1.plot(epochs, history['train_loss'], label='Training Loss',
                    color=model_color, linewidth=2, alpha=0.8)
            ax1.plot(epochs, history['val_loss'], label='Validation Loss',
                    color=model_color, linewidth=2, linestyle='--', alpha=0.8)

        ax1.set_title(f'{model_name} - Training Loss', fontsize=12, fontweight='bold')
        ax1.set_xlabel('Epochs')
        ax1.set_ylabel('Loss (MSE)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 训练历史 - MAE
        ax2 = fig.add_subplot(gs[0, 1])
        if isinstance(history, dict):
            ax2.plot(epochs, history['train_mae'], label='Training MAE',
                    color=model_color, linewidth=2, alpha=0.8)
            ax2.plot(epochs, history['val_mae'], label='Validation MAE',
                    color=model_color, linewidth=2, linestyle='--', alpha=0.8)

        ax2.set_title(f'{model_name} - Training MAE', fontsize=12, fontweight='bold')
        ax2.set_xlabel('训练轮次')
        ax2.set_ylabel('平均绝对误差')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 预测 vs 真实值散点图
        ax3 = fig.add_subplot(gs[1, 0])
        if 'predictions' in results and 'test' in results['predictions']:
            y_true, y_pred = results['predictions']['test']

            # 随机采样
            sample_size = min(500, len(y_true))
            if len(y_true) > sample_size:
                indices = np.random.choice(len(y_true), sample_size, replace=False)
                y_true_sample = y_true[indices]
                y_pred_sample = y_pred[indices]
            else:
                y_true_sample = y_true
                y_pred_sample = y_pred

            ax3.scatter(y_true_sample, y_pred_sample, alpha=0.6, s=20,
                       color=model_color, edgecolors='white', linewidth=0.5)

            # 完美预测线
            min_val = min(y_true_sample.min(), y_pred_sample.min())
            max_val = max(y_true_sample.max(), y_pred_sample.max())
            ax3.plot([min_val, max_val], [min_val, max_val],
                    'r--', linewidth=2, alpha=0.8, label='Perfect Prediction')

            ax3.set_xlabel('True Values')
            ax3.set_ylabel('Predicted Values')
            ax3.set_title(f'{model_name} - Predictions vs True Values')
            ax3.legend()

            # 添加R^2值
            r2 = r2_score(y_true, y_pred)
            ax3.text(0.05, 0.95, f'R^2 = {r2:.3f}',
                    transform=ax3.transAxes,
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                    fontsize=10, fontweight='bold')

        # 4. 时间序列预测对比
        ax4 = fig.add_subplot(gs[1, 1])
        if 'predictions' in results and 'test' in results['predictions']:
            y_true, y_pred = results['predictions']['test']
            display_points = min(300, len(y_true))
            time_steps = range(display_points)

            ax4.plot(time_steps, y_true[:display_points],
                    label='True Values', color='#212121', linewidth=2.5, alpha=1.0)
            ax4.plot(time_steps, y_pred[:display_points],
                    label=f'{model_name} Predictions', color=model_color,
                    linewidth=2.5, alpha=0.9)

            ax4.set_title(f'{model_name} - Time Series Prediction Comparison')
            ax4.set_xlabel('Time Steps')
            ax4.set_ylabel('Power (Normalized)')
            ax4.legend()
            ax4.grid(True, alpha=0.3)

        # 5. 误差分布
        ax5 = fig.add_subplot(gs[2, 0])
        if 'predictions' in results and 'test' in results['predictions']:
            y_true, y_pred = results['predictions']['test']
            errors = y_pred - y_true
            ax5.hist(errors, bins=50, alpha=0.7, color=model_color,
                    edgecolor='white', density=True)
            ax5.axvline(x=0, color='red', linestyle='--', linewidth=2)
            ax5.set_title(f'{model_name} - Error Distribution')
            ax5.set_xlabel('Prediction Error')
            ax5.set_ylabel('Density')
            ax5.grid(True, alpha=0.3)

            # 添加统计信息
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            ax5.text(0.05, 0.95, f'Mean: {mean_error:.4f}\nStd Dev: {std_error:.4f}',
                    transform=ax5.transAxes,
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                    fontsize=10, verticalalignment='top')

        # 6. 性能指标柱状图
        ax6 = fig.add_subplot(gs[2, 1])
        if 'test' in results:
            metrics = results['test']
            metric_names = ['MAE', 'MSE', 'R2']
            metric_values = [metrics[name] for name in metric_names if name in metrics]
            metric_labels = [name for name in metric_names if name in metrics]

            bars = ax6.bar(metric_labels, metric_values, color=model_color, alpha=0.8)
            ax6.set_title(f'{model_name} - Test Set Performance Metrics')
            ax6.set_ylabel('Metric Values')

            # 添加数值标签
            for bar, value in zip(bars, metric_values):
                height = bar.get_height()
                ax6.text(bar.get_x() + bar.get_width()/2., height,
                        f'{value:.4f}', ha='center', va='bottom', fontsize=9)

        plt.suptitle(f'{model_name} Model Complete Analysis', fontsize=16, fontweight='bold')

        save_file = os.path.join(save_dir, f'{model_name}_Model_Complete_Analysis.png')
        plt.savefig(save_file, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ {model_name} model complete analysis saved: {os.path.basename(save_file)}")
        return save_file

    def _plot_single_model_analysis_separated(self, model_name: str, results: Dict, history: Dict,
                                            save_dir: str, experiment_name: str) -> List[str]:
        """分离式单个模型分析图表"""
        saved_files = []
        model_color = self._get_high_contrast_color(model_name, 0)

        # 1. 训练历史图
        if history:
            plt.figure(figsize=(14, 8))

            # 检查训练历史的键名格式
            train_loss_key = 'train_loss' if 'train_loss' in history else 'loss'
            val_loss_key = 'val_loss' if 'val_loss' in history else 'val_loss'
            train_mae_key = 'train_mae' if 'train_mae' in history else 'mae'
            val_mae_key = 'val_mae' if 'val_mae' in history else 'val_mae'

            if train_loss_key in history:
                epochs = range(1, len(history[train_loss_key]) + 1)

                plt.subplot(1, 2, 1)
                plt.plot(epochs, history[train_loss_key], label='Training Loss', color=model_color, linewidth=2.5)
                if val_loss_key in history:
                    plt.plot(epochs, history[val_loss_key], label='Validation Loss',
                            color=model_color, linewidth=2.5, linestyle='--', alpha=0.8)
                plt.title(f'{model_name} - Loss Function')
                plt.xlabel('Epochs')
                plt.ylabel('Loss')
                plt.legend()
                plt.grid(True, alpha=0.3)

                plt.subplot(1, 2, 2)
                if train_mae_key in history:
                    plt.plot(epochs, history[train_mae_key], label='Training MAE', color=model_color, linewidth=2.5)
                    if val_mae_key in history:
                        plt.plot(epochs, history[val_mae_key], label='Validation MAE',
                                color=model_color, linewidth=2.5, linestyle='--', alpha=0.8)
                plt.title(f'{model_name} - MAE Metrics')
                plt.xlabel('Epochs')
                plt.ylabel('MAE')
                plt.legend()
                plt.grid(True, alpha=0.3)

            plt.suptitle(f'{model_name} - Training History', fontsize=16, fontweight='bold')
            plt.tight_layout()

            history_file = os.path.join(save_dir, f'{model_name}_Training_History.png')
            plt.savefig(history_file, dpi=300, bbox_inches='tight')
            plt.close()
            saved_files.append(history_file)
            print(f"✅ {model_name}训练历史图已保存: {os.path.basename(history_file)}")

        # 2. 时间序列预测图
        if 'predictions' in results and 'test' in results['predictions']:
            y_true, y_pred = results['predictions']['test']
            display_points = min(300, len(y_true))
            time_steps = range(display_points)

            plt.figure(figsize=(16, 8))
            plt.plot(time_steps, y_true[:display_points],
                    label='True Values', color='#212121', linewidth=2.5, alpha=1.0)
            plt.plot(time_steps, y_pred[:display_points],
                    label=f'{model_name} Predictions', color=model_color,
                    linewidth=2.5, alpha=0.9)

            plt.title(f'{model_name} - Time Series Prediction', fontsize=16, fontweight='bold')
            plt.xlabel('Time Steps', fontsize=12)
            plt.ylabel('Power (Normalized)', fontsize=12)
            plt.legend(fontsize=12)
            plt.grid(True, alpha=0.3)
            plt.tight_layout()

            timeseries_file = os.path.join(save_dir, f'{model_name}_Time_Series_Prediction.png')
            plt.savefig(timeseries_file, dpi=300, bbox_inches='tight')
            plt.close()
            saved_files.append(timeseries_file)
            print(f"✅ {model_name}时间序列预测图已保存: {os.path.basename(timeseries_file)}")

        # 3. 性能指标图
        if 'test' in results:
            metrics = results['test']
            metric_names = ['MAE', 'MSE', 'RMSE', 'R^2']
            metric_values = [metrics[name] for name in metric_names if name in metrics]
            metric_labels = [name for name in metric_names if name in metrics]

            plt.figure(figsize=(10, 8))
            bars = plt.bar(metric_labels, metric_values, color=model_color, alpha=0.8,
                          edgecolor='white', linewidth=2)
            plt.title(f'{model_name} - Performance Metrics', fontsize=16, fontweight='bold')
            plt.ylabel('Metric Values', fontsize=12)

            # 添加数值标签
            for bar, value in zip(bars, metric_values):
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height,
                        f'{value:.4f}', ha='center', va='bottom', fontsize=11, fontweight='bold')

            plt.grid(True, alpha=0.3, axis='y')
            plt.tight_layout()

            metrics_file = os.path.join(save_dir, f'{model_name}_Performance_Metrics.png')
            plt.savefig(metrics_file, dpi=300, bbox_inches='tight')
            plt.close()
            saved_files.append(metrics_file)
            print(f"✅ {model_name}性能指标图已保存: {os.path.basename(metrics_file)}")

        return saved_files

    def _plot_data_distribution_universal(self, results: Dict, save_dir: str, experiment_name: str) -> str:
        """通用数据分布对比图"""
        dataset = 'test'
        model_names = [name for name in results.keys()
                      if 'predictions' in results[name] and dataset in results[name]['predictions']]

        if not model_names:
            print(f"⚠️ 没有找到测试集预测结果，跳过数据分布图")
            return ""

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # 获取第一个模型的真实值
        y_true, _ = results[model_names[0]]['predictions'][dataset]

        # 1. 真实值分布
        ax1 = axes[0, 0]
        ax1.hist(y_true, bins=50, alpha=0.7, color='#212121', edgecolor='white')
        ax1.set_title('True Values Distribution', fontsize=12, fontweight='bold')
        ax1.set_xlabel('Power Values')
        ax1.set_ylabel('Frequency')
        ax1.grid(True, alpha=0.3)

        # 2. 预测值分布对比
        ax2 = axes[0, 1]
        for model_name in model_names:
            _, y_pred = results[model_name]['predictions'][dataset]
            color = self._get_model_color(model_name)
            ax2.hist(y_pred, bins=50, alpha=0.6, color=color,
                    edgecolor='white', label=f'{model_name} Predictions')

        ax2.hist(y_true, bins=50, alpha=0.8, color='#212121',
                edgecolor='white', label='True Values', linewidth=2)
        ax2.set_title('Predicted Values Distribution Comparison', fontsize=12, fontweight='bold')
        ax2.set_xlabel('Power Values')
        ax2.set_ylabel('Frequency')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. Q-Q图对比
        ax3 = axes[1, 0]
        from scipy import stats
        for model_name in model_names:
            _, y_pred = results[model_name]['predictions'][dataset]
            color = self._get_model_color(model_name)
            stats.probplot(y_pred, dist="norm", plot=ax3)
            ax3.get_lines()[-1].set_color(color)
            ax3.get_lines()[-1].set_label(f'{model_name}')

        ax3.set_title('Q-Q Plot (Normality Test)', fontsize=12, fontweight='bold')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 残差分布对比
        ax4 = axes[1, 1]
        for model_name in model_names:
            _, y_pred = results[model_name]['predictions'][dataset]
            residuals = y_pred - y_true
            color = self._get_model_color(model_name)
            ax4.hist(residuals, bins=30, alpha=0.6, color=color,
                    edgecolor='white', label=f'{model_name} Residuals')

        ax4.axvline(x=0, color='red', linestyle='--', linewidth=2)
        ax4.set_title('Residual Distribution Comparison', fontsize=12, fontweight='bold')
        ax4.set_xlabel('Residual Values')
        ax4.set_ylabel('Frequency')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.suptitle('Data Distribution Analysis', fontsize=16, fontweight='bold')
        plt.tight_layout()

        save_file = os.path.join(save_dir, 'Data_Distribution_Analysis.png')
        plt.savefig(save_file, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ 数据分布分析图已保存: {os.path.basename(save_file)}")
        return save_file

    def _plot_data_distribution_separated(self, results: Dict, save_dir: str, experiment_name: str) -> List[str]:
        """分离式数据分布分析图表"""
        saved_files = []
        dataset = 'test'
        model_names = [name for name in results.keys()
                      if 'predictions' in results[name] and dataset in results[name]['predictions']]

        if not model_names:
            return saved_files

        # 获取第一个模型的真实值
        y_true, _ = results[model_names[0]]['predictions'][dataset]

        # 1. 真实值分布图
        plt.figure(figsize=(12, 8))
        plt.hist(y_true, bins=50, alpha=0.7, color=self.high_contrast_colors['primary'],
                edgecolor='white', linewidth=1.5)
        plt.title('True Values Distribution', fontsize=16, fontweight='bold')
        plt.xlabel('Power Values', fontsize=12)
        plt.ylabel('Frequency', fontsize=12)
        plt.grid(True, alpha=0.3)

        # 添加统计信息
        mean_true = np.mean(y_true)
        std_true = np.std(y_true)
        plt.text(0.05, 0.95, f'均值: {mean_true:.4f}\n标准差: {std_true:.4f}',
                transform=plt.gca().transAxes,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                fontsize=12, verticalalignment='top', fontweight='bold')

        plt.tight_layout()

        true_dist_file = os.path.join(save_dir, 'True_Values_Distribution.png')
        plt.savefig(true_dist_file, dpi=300, bbox_inches='tight')
        plt.close()
        saved_files.append(true_dist_file)
        print(f"✅ 真实值分布图已保存: {os.path.basename(true_dist_file)}")

        # 2. 各模型预测值分布对比
        plt.figure(figsize=(14, 8))
        for i, model_name in enumerate(model_names):
            _, y_pred = results[model_name]['predictions'][dataset]
            model_color = self._get_high_contrast_color(model_name, i)
            plt.hist(y_pred, bins=50, alpha=0.6,
                    label=f'{model_name} Predictions',
                    color=model_color, edgecolor='white', linewidth=1)

        # 添加真实值分布作为参考
        plt.hist(y_true, bins=50, alpha=0.8,
                label='True Values', color=self.high_contrast_colors['primary'],
                edgecolor='white', linewidth=1.5)

        plt.title('Predicted Values Distribution Comparison', fontsize=16, fontweight='bold')
        plt.xlabel('Power Values', fontsize=12)
        plt.ylabel('Frequency', fontsize=12)
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        pred_dist_file = os.path.join(save_dir, 'Predicted_Values_Distribution_Comparison.png')
        plt.savefig(pred_dist_file, dpi=300, bbox_inches='tight')
        plt.close()
        saved_files.append(pred_dist_file)
        print(f"✅ 预测值分布对比图已保存: {os.path.basename(pred_dist_file)}")

        return saved_files

    def _plot_relative_percentage_error_with_inset(self, results: Dict, dataset: str, save_dir: str, experiment_name: str) -> str:
        """
        绘制相对百分比误差时间序列图（带插图）
        模仿论文中的可视化风格

        Args:
            results: 模型结果字典
            dataset: 数据集名称
            save_dir: 保存目录
            experiment_name: 实验名称

        Returns:
            保存的图片文件路径
        """
        # 获取有预测结果的模型
        available_models = [name for name in results.keys()
                           if 'predictions' in results[name] and dataset in results[name]['predictions']]

        # 根据配置过滤模型
        model_names = self._filter_models_by_config(available_models)

        if not model_names:
            print(f"⚠️ 没有找到启用的{dataset}集预测结果，跳过相对百分比误差图")
            return ""

        print(f"📊 根据配置显示模型: {', '.join(model_names)}")

        # 设置学术风格
        setup_matplotlib()

        # 创建图形
        fig, ax = plt.subplots(figsize=(12, 8))

        # 定义线型和标记样式
        line_styles = ['-', '--', '-.', ':', '-', '--']
        markers = ['o', 's', '^', 'D', 'v', 'p']

        # 获取数据并计算相对百分比误差
        display_points = min(400, len(results[model_names[0]]['predictions'][dataset][0]))
        time_steps = range(display_points)

        # 存储所有模型的相对百分比误差
        all_errors = {}

        for i, model_name in enumerate(model_names):
            y_true, y_pred = results[model_name]['predictions'][dataset]

            # 计算相对百分比误差，避免除零
            with np.errstate(divide='ignore', invalid='ignore'):
                relative_error = ((y_pred[:display_points] - y_true[:display_points]) /
                                np.where(y_true[:display_points] != 0, y_true[:display_points], 1)) * 100
                # 处理无穷大和NaN值
                relative_error = np.where(np.isfinite(relative_error), relative_error, 0)

            all_errors[model_name] = relative_error

            # 获取颜色
            color = self._get_high_contrast_color(model_name, i)

            # 绘制主图
            ax.plot(time_steps, relative_error,
                   label=model_name,
                   color=color,
                   linewidth=2,
                   linestyle=line_styles[i % len(line_styles)],
                   alpha=0.8)

        # 设置主图样式
        ax.set_xlabel('Time', fontsize=14)
        ax.set_ylabel('Relative percentage error', fontsize=14)
        ax.set_title(f'Relative Percentage Error Comparison - {experiment_name}', fontsize=16, pad=20)
        ax.grid(True, alpha=0.3)
        ax.legend(loc='upper right', fontsize=10)

        # 设置Y轴范围，确保显示效果好
        all_values = np.concatenate(list(all_errors.values()))
        y_min, y_max = np.percentile(all_values, [5, 95])
        y_range = y_max - y_min
        ax.set_ylim(y_min - 0.1 * y_range, y_max + 0.1 * y_range)

        # 添加插图（inset）- 显示局部细节
        # 选择一个有趣的区间进行放大
        inset_start = int(display_points * 0.7)  # 从70%位置开始
        inset_end = int(display_points * 0.9)    # 到90%位置结束

        # 创建插图
        axins = inset_axes(ax, width="40%", height="40%", loc='upper left',
                          bbox_to_anchor=(0.05, 0.55, 0.4, 0.4), bbox_transform=ax.transAxes)

        # 在插图中绘制放大的区域
        inset_time = range(inset_start, inset_end)
        for i, model_name in enumerate(model_names):
            color = self._get_high_contrast_color(model_name, i)
            axins.plot(inset_time, all_errors[model_name][inset_start:inset_end],
                      color=color,
                      linewidth=1.5,
                      linestyle=line_styles[i % len(line_styles)],
                      marker=markers[i % len(markers)],
                      markersize=3,
                      alpha=0.9)

        # 设置插图样式
        axins.grid(True, alpha=0.3)
        axins.tick_params(labelsize=8)

        # 在主图上标记插图区域
        ax.axvspan(inset_start, inset_end, alpha=0.2, color='gray', linestyle='--')

        # 添加箭头指向插图区域
        from matplotlib.patches import ConnectionPatch
        con1 = ConnectionPatch(xyA=(inset_start, ax.get_ylim()[1]), xyB=(0, 1),
                              coordsA='data', coordsB='axes fraction',
                              axesA=ax, axesB=axins,
                              arrowstyle='->', color='gray', alpha=0.7)
        ax.add_patch(con1)

        con2 = ConnectionPatch(xyA=(inset_end, ax.get_ylim()[1]), xyB=(1, 1),
                              coordsA='data', coordsB='axes fraction',
                              axesA=ax, axesB=axins,
                              arrowstyle='->', color='gray', alpha=0.7)
        ax.add_patch(con2)

        # 保存图片
        filename = f"{experiment_name}_relative_percentage_error_with_inset_{dataset}.png"
        filepath = os.path.join(save_dir, filename)
        plt.tight_layout()
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ 相对百分比误差图（带插图）已保存: {os.path.basename(filepath)}")
        return filepath

    def _plot_relative_percentage_error_simple(self, results: Dict, dataset: str, save_dir: str, experiment_name: str) -> str:
        """
        绘制简化版相对百分比误差时间序列图（无插图）
        时间步长为100，适合快速对比

        Args:
            results: 模型结果字典
            dataset: 数据集名称
            save_dir: 保存目录
            experiment_name: 实验名称

        Returns:
            保存的图片文件路径
        """
        # 获取有预测结果的模型
        available_models = [name for name in results.keys()
                           if 'predictions' in results[name] and dataset in results[name]['predictions']]

        # 根据配置过滤模型
        model_names = self._filter_models_by_config(available_models)

        if not model_names:
            print(f"⚠️ 没有找到启用的{dataset}集预测结果，跳过相对百分比误差图")
            return ""

        print(f"📊 根据配置显示模型: {', '.join(model_names)}")

        # 设置学术风格
        setup_matplotlib()

        # 创建图形 - 简化版本，无插图
        fig, ax = plt.subplots(figsize=(12, 6))

        # 定义线型和标记样式
        line_styles = ['-', '--', '-.', ':', '-', '--']

        # 获取数据并计算相对百分比误差 - 缩减至100个时间步
        display_points = min(100, len(results[model_names[0]]['predictions'][dataset][0]))
        time_steps = range(display_points)

        # 存储所有模型的相对百分比误差
        all_errors = {}

        for i, model_name in enumerate(model_names):
            y_true, y_pred = results[model_name]['predictions'][dataset]

            # 计算相对百分比误差，避免除零
            with np.errstate(divide='ignore', invalid='ignore'):
                relative_error = ((y_pred[:display_points] - y_true[:display_points]) /
                                np.where(y_true[:display_points] != 0, y_true[:display_points], 1)) * 100
                # 处理无穷大和NaN值
                relative_error = np.where(np.isfinite(relative_error), relative_error, 0)

            all_errors[model_name] = relative_error

            # 获取颜色
            color = self._get_high_contrast_color(model_name, i)

            # 绘制主图
            ax.plot(time_steps, relative_error,
                   label=model_name,
                   color=color,
                   linewidth=2.5,
                   linestyle=line_styles[i % len(line_styles)],
                   alpha=0.9)

        # 设置图表样式
        ax.set_xlabel('Time', fontsize=14, fontweight='bold')
        ax.set_ylabel('Relative percentage error (%)', fontsize=14, fontweight='bold')
        ax.set_title(f'Relative Percentage Error Comparison - {experiment_name}',
                    fontsize=16, fontweight='bold', pad=20)
        ax.grid(True, alpha=0.3)
        ax.legend(loc='best', fontsize=11, frameon=True, fancybox=True, shadow=True)

        # 设置Y轴范围，确保显示效果好
        all_values = np.concatenate(list(all_errors.values()))
        y_min, y_max = np.percentile(all_values, [5, 95])
        y_range = y_max - y_min
        ax.set_ylim(y_min - 0.1 * y_range, y_max + 0.1 * y_range)

        # 设置X轴范围
        ax.set_xlim(0, display_points - 1)

        # 美化图表
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(1.2)
        ax.spines['bottom'].set_linewidth(1.2)

        # 保存图片
        filename = f"{experiment_name}_relative_percentage_error_{dataset}.png"
        filepath = os.path.join(save_dir, filename)
        plt.tight_layout()
        plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        print(f"✅ 简化版相对百分比误差图已保存: {os.path.basename(filepath)}")
        return filepath

    def quick_visualize(self,
                       models_data: Dict[str, Tuple[Dict, Dict]],
                       experiment_name: str = "Quick_Analysis",
                       save_path: Optional[str] = None) -> List[str]:
        """
        快速可视化接口 - 简化的使用方式

        Args:
            models_data: {model_name: (results, history)} 格式的数据
            experiment_name: 实验名称
            save_path: 保存路径

        Returns:
            生成的图片文件路径列表
        """
        # 转换数据格式
        results = {}
        histories = {}

        for model_name, (model_results, model_history) in models_data.items():
            results[model_name] = model_results
            if model_history is not None:
                histories[model_name] = model_history

        return self.generate_complete_visualization_suite(
            results=results,
            histories=histories,
            save_path=save_path,
            experiment_name=experiment_name
        )

# 便捷函数
def create_universal_visualizations(models_data: Dict[str, Tuple[Dict, Dict]],
                                  experiment_name: str = "Model_Comparison",
                                  save_path: Optional[str] = None) -> List[str]:
    """
    创建通用可视化的便捷函数

    Args:
        models_data: {model_name: (results, history)} 格式的数据
        experiment_name: 实验名称
        save_path: 保存路径

    Returns:
        生成的图片文件路径列表

    Example:
        # 使用示例
        models_data = {
            'BiGRU': (bigru_results, bigru_history),
            'DPTAM-BiGRU': (dptam_results, dptam_history)
        }
        files = create_universal_visualizations(models_data, "BiGRU_vs_DPTAM")
    """
    visualizer = UniversalVisualizer()
    return visualizer.quick_visualize(models_data, experiment_name, save_path)

# 测试代码
if __name__ == "__main__":
    print("🎨 通用可视化系统测试")
    print("=" * 50)

    # 创建可视化工具实例
    visualizer = UniversalVisualizer()

    # 测试颜色配置
    print("模型颜色映射:")
    test_models = ['GRU', 'LSTM', 'BiGRU', 'DPTAM-BiGRU', 'CustomModel']
    for model in test_models:
        color = visualizer._get_model_color(model)
        print(f"  {model}: {color}")

    print("\n✅ 通用可视化系统初始化完成")
    print("💡 使用 create_universal_visualizations() 函数快速生成可视化")
