"""内存优化的配置文件，用于解决CUDA内存不足问题"""importosimporttorchfromdatetimeimportdatetime#项目根目录PROJECT_ROOT=os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))#设备配置-优先使用CPU以避免内存问题iftorch.cuda.is_available():#检查GPU内存gpu_memory=torch.cuda.get_device_properties(0).total_memory/1024**3#GBprint(f"GPU内存:{gpu_memory:.1f}GB")ifgpu_memory<4.0:#如果GPU内存小于4GB，使用CPUDEVICE=torch.device('cpu')print("GPU内存不足，使用CPU进行训练")else:DEVICE=torch.device('cuda')print("使用GPU进行训练")else:DEVICE=torch.device('cpu')print("CUDA不可用，使用CPU进行训练")defgenerate_timestamped_paths(base_timestamp:str=None)->dict:"""生成带时间戳的结果路径（内存优化版本）Args:base_timestamp:基础时间戳字符串，如果为None则生成当前时间戳Returns:包含时间戳路径的字典"""ifbase_timestampisNone:#生成当前时间戳：YYYY-MM-DD_HH-MM-SSbase_timestamp=datetime.now().strftime("%Y-%m-%d_%H-%M-%S")#创建时间戳结果目录timestamped_results_dir=os.path.join(PROJECT_ROOT,'results',base_timestamp)return{'raw_data':os.path.join(PROJECT_ROOT,'data','raw'),'processed_data':os.path.join(PROJECT_ROOT,'data','processed'),'results_root':timestamped_results_dir,#新增：时间戳根目录'models':os.path.join(timestamped_results_dir,'models'),'figures':os.path.join(timestamped_results_dir,'figures'),'reports':os.path.join(timestamped_results_dir,'reports'),'timestamp':base_timestamp#新增：时间戳字符串}#默认数据路径（保持向后兼容）DATA_PATHS={'raw_data':os.path.join(PROJECT_ROOT,'data','raw'),'processed_data':os.path.join(PROJECT_ROOT,'data','processed'),'models':os.path.join(PROJECT_ROOT,'results','models'),'figures':os.path.join(PROJECT_ROOT,'results','figures'),'reports':os.path.join(PROJECT_ROOT,'results','reports')}#全局变量：当前训练会话的时间戳路径CURRENT_TRAINING_PATHS=Nonedefsetup_training_session(timestamp:str=None)->dict:"""设置新的训练会话，生成时间戳路径并创建目录（内存优化版本）Args:timestamp:可选的时间戳字符串，如果为None则生成当前时间戳Returns:时间戳路径字典"""globalCURRENT_TRAINING_PATHS#生成时间戳路径CURRENT_TRAINING_PATHS=generate_timestamped_paths(timestamp)#创建所有必要的目录directories_to_create=[CURRENT_TRAINING_PATHS['results_root'],CURRENT_TRAINING_PATHS['models'],CURRENT_TRAINING_PATHS['figures'],CURRENT_TRAINING_PATHS['reports']]fordirectoryindirectories_to_create:os.makedirs(directory,exist_ok=True)print(f"内存优化训练会话已设置，时间戳:{CURRENT_TRAINING_PATHS['timestamp']}")print(f"结果将保存到:{CURRENT_TRAINING_PATHS['results_root']}")returnCURRENT_TRAINING_PATHSdefget_current_paths()->dict:"""获取当前训练会话的路径（内存优化版本）Returns:当前路径字典，如果未设置训练会话则返回默认路径"""ifCURRENT_TRAINING_PATHSisNone:print("警告:未设置训练会话，使用默认路径")returnDATA_PATHSreturnCURRENT_TRAINING_PATHS#内存优化的模型配置MEMORY_OPTIMIZED_CONFIG={'sequence_length':12,#大幅减少序列长度'train_ratio':0.8,'val_ratio':0.1,'test_ratio':0.1,'batch_size':8,#大幅减少批次大小'epochs':30,#减少训练轮数'patience':8,#减少早停耐心值'learning_rate':0.001,'dropout_rate':0.2,#减少dropout'device':DEVICE,'random_seed':42,'num_workers':0#设置为0避免多进程问题}#内存优化的GRU配置MEMORY_OPTIMIZED_GRU_CONFIG={'name':'GRU','gru_units':[32,16],#减少隐藏单元数'dense_units':[16,8]#减少全连接层单元数}#内存优化的LSTM配置MEMORY_OPTIMIZED_LSTM_CONFIG={'name':'LSTM','lstm_units':[32,16],#减少隐藏单元数'dense_units':[16,8]#减少全连接层单元数}#内存优化的BiGRU配置MEMORY_OPTIMIZED_BIGRU_CONFIG={'bigru_units':[32,16],#减少隐藏单元数（双向会自动翻倍）'dense_units':[16,8],#减少全连接层单元数'dropout_rate':0.2,'bidirectional':True}#特征列表（保持不变）FEATURE_COLUMNS=['Wind_speed_10m','Wind_direction_10m','Wind_speed_30m','Wind_direction_30m','Wind_speed_50m','Wind_direction_50m','Wind_speed_hub','Wind_direction_hub','Air_temperature','Atmosphere','Relative_humidity','hour_sin','hour_cos','day_sin','day_cos','month_sin','month_cos','wind_speed_diff','wind_direction_diff','Power_lag1','Power_lag2','Power_lag3','Power_ma3','Power_ma6','Wind_speed_ma3']#颜色配置（保持不变）ACADEMIC_COLORS={'primary':'#1E88E5',#鲜艳蓝色'secondary':'#E91E63',#鲜艳粉红色'accent':'#FF9800',#鲜艳橙色'success':'#4CAF50',#鲜艳绿色'info':'#00BCD4',#鲜艳青色'warning':'#FFC107',#鲜艳黄色'light':'#F5F5F5',#浅灰色'dark':'#212121'#深黑色}MODEL_COLORS={'GRU':'#1E88E5',#鲜艳蓝色'LSTM':'#E91E63',#鲜艳粉红色'BiGRU':'#4CAF50',#鲜艳绿色'Actual':'#212121'#深黑色}defsetup_matplotlib():"""设置matplotlib的中文字体和样式"""importmatplotlib.pyplotaspltimportmatplotlib#设置中文字体plt.rcParams['font.sans-serif']=['SimHei','DejaVuSans']plt.rcParams['axes.unicode_minus']=False#设置图表样式plt.style.use('default')matplotlib.rcParams.update({'figure.figsize':(10,6),'font.size':10,'axes.titlesize':12,'axes.labelsize':10,'xtick.labelsize':9,'ytick.labelsize':9,'legend.fontsize':9,'figure.titlesize':14,'lines.linewidth':2,'grid.alpha':0.3,'axes.grid':True,'axes.spines.top':False,'axes.spines.right':False,'axes.edgecolor':'gray','axes.linewidth':0.8})print("成功设置字体:SimHei")print(f"内存优化配置加载完成")print(f"设备:{DEVICE}")print(f"批次大小:{MEMORY_OPTIMIZED_CONFIG['batch_size']}")print(f"序列长度:{MEMORY_OPTIMIZED_CONFIG['sequence_length']}")