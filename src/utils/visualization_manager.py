"""可视化管理器统一管理所有实验的可视化需求，提供标准化的可视化接口"""importosimportsysfromtypingimportDict,List,Optional,Tuple,Any,Union#添加项目根目录到系统路径sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))fromsrc.utils.universal_visualizerimportUniversalVisualizer,create_universal_visualizations#传统可视化系统已移除，只使用通用可视化系统fromsrc.utils.configimportget_current_pathsclassVisualizationManager:"""可视化管理器-统一管理所有可视化需求"""def__init__(self):"""初始化可视化管理器"""self.universal_visualizer=UniversalVisualizer()#传统可视化系统已移除defauto_visualize_experiment(self,models_data:Dict[str,Tuple[Dict,Dict]],experiment_name:str,save_path:Optional[str]=None,include_legacy:bool=True)->Dict[str,List[str]]:"""自动为实验生成完整的可视化套件Args:models_data:{model_name:(results,history)}格式的数据experiment_name:实验名称save_path:保存路径include_legacy:是否包含传统的可视化方法Returns:{'universal':[files],'legacy':[files]}格式的文件路径字典"""ifsave_pathisNone:save_path=get_current_paths()['figures']generated_files={'universal':[],'legacy':[]}print(f"\n开始为实验'{experiment_name}'生成完整可视化套件...")print(f"检测到模型:{','.join(models_data.keys())}")#1.使用通用可视化系统try:universal_files=self.universal_visualizer.quick_visualize(models_data=models_data,experiment_name=experiment_name,save_path=save_path)generated_files['universal']=universal_filesprint(f"通用可视化系统生成了{len(universal_files)}个图表")exceptExceptionase:print(f"通用可视化系统出错:{str(e)}")#传统可视化系统已移除，只使用通用系统total_files=len(generated_files['universal'])+len(generated_files['legacy'])print(f"\n实验'{experiment_name}'可视化完成！")print(f"总共生成{total_files}个图表文件")returngenerated_files#传统可视化方法已移除，只使用通用可视化系统defvisualize_single_model(self,model_name:str,results:Dict,history:Dict,experiment_name:str,save_path:Optional[str]=None)->List[str]:"""为单个模型生成详细的可视化分析Args:model_name:模型名称results:模型结果history:训练历史experiment_name:实验名称save_path:保存路径Returns:生成的图片文件路径列表"""ifsave_pathisNone:save_path=get_current_paths()['figures']models_data={model_name:(results,history)}returnself.auto_visualize_experiment(models_data=models_data,experiment_name=f"{experiment_name}_{model_name}",save_path=save_path,include_legacy=False#单模型不需要传统对比图)['universal']defcompare_models(self,models_data:Dict[str,Tuple[Dict,Dict]],experiment_name:str="Model_Comparison",save_path:Optional[str]=None)->Dict[str,List[str]]:"""比较多个模型的性能Args:models_data:{model_name:(results,history)}格式的数据experiment_name:实验名称save_path:保存路径Returns:生成的图片文件路径字典"""returnself.auto_visualize_experiment(models_data=models_data,experiment_name=experiment_name,save_path=save_path,include_legacy=True)#全局可视化管理器实例_global_viz_manager=Nonedefget_visualization_manager()->VisualizationManager:"""获取全局可视化管理器实例"""global_global_viz_managerif_global_viz_managerisNone:_global_viz_manager=VisualizationManager()return_global_viz_manager#便捷函数defauto_visualize(models_data:Dict[str,Tuple[Dict,Dict]],experiment_name:str,save_path:Optional[str]=None)->Dict[str,List[str]]:"""自动生成可视化的便捷函数Args:models_data:{model_name:(results,history)}格式的数据experiment_name:实验名称save_path:保存路径Returns:生成的图片文件路径字典Example:#使用示例models_data={'BiGRU':(bigru_results,bigru_history),'DPTAM-BiGRU':(dptam_results,dptam_history)}files=auto_visualize(models_data,"BiGRU_vs_DPTAM")"""manager=get_visualization_manager()returnmanager.auto_visualize_experiment(models_data,experiment_name,save_path)defvisualize_single(model_name:str,results:Dict,history:Dict,experiment_name:str,save_path:Optional[str]=None)->List[str]:"""单模型可视化的便捷函数Args:model_name:模型名称results:模型结果history:训练历史experiment_name:实验名称save_path:保存路径Returns:生成的图片文件路径列表"""manager=get_visualization_manager()returnmanager.visualize_single_model(model_name,results,history,experiment_name,save_path)#测试代码if__name__=="__main__":print("可视化管理器测试")print("="*50)#创建可视化管理器manager=VisualizationManager()print("可视化管理器初始化完成")print("使用auto_visualize()函数快速生成所有可视化")print("使用visualize_single()函数为单个模型生成可视化")