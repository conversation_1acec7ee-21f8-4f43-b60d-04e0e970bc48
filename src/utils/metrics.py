"""评估指标模块，提供各种模型评估指标的计算"""importnumpyasnpimportpandasaspdfromsklearn.metricsimportmean_absolute_error,mean_squared_error,r2_scorefromtypingimportDict,List,Tuple,Optional,Anyimportsysimportos#添加项目根目录到系统路径sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))fromsrc.utils.configimportDATA_PATHSclassModelEvaluator:"""模型评估器，提供各种评估指标的计算"""def__init__(self):"""初始化评估器"""self.metrics_names=['MAE','MSE','RMSE','R2','MAPE','SMAPE','WAPE']defcalculate_mae(self,y_true:np.ndarray,y_pred:np.ndarray)->float:"""计算平均绝对误差(MeanAbsoluteError)"""returnmean_absolute_error(y_true,y_pred)defcalculate_mse(self,y_true:np.ndarray,y_pred:np.ndarray)->float:"""计算均方误差(MeanSquaredError)"""returnmean_squared_error(y_true,y_pred)defcalculate_rmse(self,y_true:np.ndarray,y_pred:np.ndarray)->float:"""计算均方根误差(RootMeanSquaredError)"""returnnp.sqrt(mean_squared_error(y_true,y_pred))defcalculate_r2(self,y_true:np.ndarray,y_pred:np.ndarray)->float:"""计算决定系数(R-squared)"""returnr2_score(y_true,y_pred)defcalculate_mape(self,y_true:np.ndarray,y_pred:np.ndarray)->float:"""计算平均绝对百分比误差(MeanAbsolutePercentageError)Args:y_true:真实值y_pred:预测值Returns:MAPE值(百分比)"""#避免除以零mask=y_true!=0ifnp.sum(mask)==0:returnnp.infreturnnp.mean(np.abs((y_true[mask]-y_pred[mask])/y_true[mask]))*100defcalculate_smape(self,y_true:np.ndarray,y_pred:np.ndarray)->float:"""计算对称平均绝对百分比误差(SymmetricMeanAbsolutePercentageError)Args:y_true:真实值y_pred:预测值Returns:SMAPE值(百分比)"""denominator=(np.abs(y_true)+np.abs(y_pred))/2mask=denominator!=0ifnp.sum(mask)==0:returnnp.infreturnnp.mean(np.abs(y_true[mask]-y_pred[mask])/denominator[mask])*100defcalculate_wape(self,y_true:np.ndarray,y_pred:np.ndarray)->float:"""计算加权平均绝对百分比误差(WeightedAbsolutePercentageError)Args:y_true:真实值y_pred:预测值Returns:WAPE值(百分比)"""ifnp.sum(np.abs(y_true))==0:returnnp.infreturnnp.sum(np.abs(y_true-y_pred))/np.sum(np.abs(y_true))*100defcalculate_nmae(self,y_true:np.ndarray,y_pred:np.ndarray,normalization_factor:Optional[float]=None)->float:"""计算归一化平均绝对误差(NormalizedMeanAbsoluteError)常用于风电功率预测Args:y_true:真实值y_pred:预测值normalization_factor:归一化因子，如果为None则使用数据范围Returns:NMAE值(百分比)"""mae=self.calculate_mae(y_true,y_pred)ifnormalization_factorisNone:#使用数据范围作为归一化因子data_range=np.max(y_true)-np.min(y_true)ifdata_range==0:returnnp.infnormalization_factor=data_rangereturn(mae/normalization_factor)*100defcalculate_nrmse(self,y_true:np.ndarray,y_pred:np.ndarray,normalization_factor:Optional[float]=None)->float:"""计算归一化均方根误差(NormalizedRootMeanSquaredError)常用于风电功率预测Args:y_true:真实值y_pred:预测值normalization_factor:归一化因子，如果为None则使用数据范围Returns:NRMSE值(百分比)"""rmse=self.calculate_rmse(y_true,y_pred)ifnormalization_factorisNone:#使用数据范围作为归一化因子data_range=np.max(y_true)-np.min(y_true)ifdata_range==0:returnnp.infnormalization_factor=data_rangereturn(rmse/normalization_factor)*100defcalculate_skill_score(self,y_true:np.ndarray,y_pred:np.ndarray,y_persistence:Optional[np.ndarray]=None)->float:"""计算预测技能评分(SkillScore)相对于持续性预测的改进程度Args:y_true:真实值y_pred:预测值y_persistence:持续性预测值，如果为None则使用前一时刻值Returns:SkillScore值，1表示完美预测，0表示与持续性预测相同，负值表示比持续性预测差"""ify_persistenceisNone:#使用前一时刻值作为持续性预测y_persistence=np.roll(y_true,1)y_persistence[0]=y_true[0]#第一个值保持不变mse_forecast=self.calculate_mse(y_true,y_pred)mse_persistence=self.calculate_mse(y_true,y_persistence)ifmse_persistence==0:return1.0ifmse_forecast==0else-np.infreturn1-(mse_forecast/mse_persistence)defcalculate_all_metrics(self,y_true:np.ndarray,y_pred:np.ndarray)->Dict[str,float]:"""计算所有评估指标Args:y_true:真实值y_pred:预测值Returns:包含所有指标的字典"""metrics={'MAE':self.calculate_mae(y_true,y_pred),'MSE':self.calculate_mse(y_true,y_pred),'RMSE':self.calculate_rmse(y_true,y_pred),'R2':self.calculate_r2(y_true,y_pred),'MAPE':self.calculate_mape(y_true,y_pred),'SMAPE':self.calculate_smape(y_true,y_pred),'WAPE':self.calculate_wape(y_true,y_pred)}returnmetricsdefevaluate_model_performance(self,y_true_dict:Dict[str,np.ndarray],y_pred_dict:Dict[str,np.ndarray],model_name:str="Model")->Dict[str,Dict[str,float]]:"""评估模型在不同数据集上的性能Args:y_true_dict:包含不同数据集真实值的字典y_pred_dict:包含不同数据集预测值的字典model_name:模型名称Returns:包含各数据集评估结果的字典"""results={}fordatasetiny_true_dict.keys():ifdatasetiny_pred_dict:y_true=y_true_dict[dataset]y_pred=y_pred_dict[dataset]metrics=self.calculate_all_metrics(y_true,y_pred)results[dataset]=metrics#打印结果print(f"\n{model_name}-{dataset.capitalize()}集性能:")formetric_name,valueinmetrics.items():ifmetric_namein['MAPE','SMAPE','WAPE']:print(f"{metric_name}:{value:.4f}%")else:print(f"{metric_name}:{value:.4f}")returnresultsdefcompare_models(self,models_results:Dict[str,Dict[str,Dict[str,float]]],save_path:Optional[str]=None)->pd.DataFrame:"""比较多个模型的性能Args:models_results:包含多个模型评估结果的字典save_path:保存路径Returns:包含比较结果的DataFrame"""comparison_data=[]formodel_name,model_resultsinmodels_results.items():fordataset,metricsinmodel_results.items():formetric_name,valueinmetrics.items():comparison_data.append({'Model':model_name,'Dataset':dataset,'Metric':metric_name,'Value':value})df_comparison=pd.DataFrame(comparison_data)#创建透视表pivot_tables={}formetricinself.metrics_names:ifmetricindf_comparison['Metric'].values:pivot_table=df_comparison[df_comparison['Metric']==metric].pivot(index='Model',columns='Dataset',values='Value')pivot_tables[metric]=pivot_tableprint(f"\n{metric}比较:")print(pivot_table.round(4))#保存结果ifsave_path:comparison_file=os.path.join(save_path,'model_comparison.csv')df_comparison.to_csv(comparison_file,index=False)print(f"\n模型比较结果已保存到:{comparison_file}")#保存各指标的透视表formetric,pivot_tableinpivot_tables.items():metric_file=os.path.join(save_path,f'{metric}_comparison.csv')pivot_table.to_csv(metric_file)returndf_comparisondeffind_best_model(self,models_results:Dict[str,Dict[str,Dict[str,float]]],primary_metric:str='RMSE',dataset:str='test')->Tuple[str,float]:"""根据指定指标找到最佳模型Args:models_results:包含多个模型评估结果的字典primary_metric:主要评估指标dataset:数据集类型Returns:最佳模型名称和对应的指标值"""best_model=Nonebest_value=None#对于R2，值越大越好；对于其他指标，值越小越好is_higher_better=primary_metric=='R2'formodel_name,model_resultsinmodels_results.items():ifdatasetinmodel_resultsandprimary_metricinmodel_results[dataset]:value=model_results[dataset][primary_metric]ifbest_modelisNone:best_model=model_namebest_value=valueelse:ifis_higher_better:ifvalue>best_value:best_model=model_namebest_value=valueelse:ifvalue<best_value:best_model=model_namebest_value=valueprint(f"\n基于{primary_metric}指标在{dataset}集上的最佳模型:{best_model}({primary_metric}:{best_value:.4f})")returnbest_model,best_valuedefcalculate_improvement(self,baseline_value:float,improved_value:float,metric_name:str)->float:"""计算改进百分比Args:baseline_value:基准值improved_value:改进后的值metric_name:指标名称Returns:改进百分比"""ifmetric_name=='R2':#R2越大越好improvement=((improved_value-baseline_value)/baseline_value)*100else:#其他指标越小越好improvement=((baseline_value-improved_value)/baseline_value)*100returnimprovement#测试代码if__name__=="__main__":#创建评估器实例evaluator=ModelEvaluator()#创建一些测试数据np.random.seed(42)y_true=np.random.random(1000)*100y_pred1=y_true+np.random.normal(0,5,1000)#模型1y_pred2=y_true+np.random.normal(0,8,1000)#模型2#计算指标metrics1=evaluator.calculate_all_metrics(y_true,y_pred1)metrics2=evaluator.calculate_all_metrics(y_true,y_pred2)print("模型1指标:")forname,valueinmetrics1.items():print(f"{name}:{value:.4f}")print("\n模型2指标:")forname,valueinmetrics2.items():print(f"{name}:{value:.4f}")#比较模型models_results={'Model1':{'test':metrics1},'Model2':{'test':metrics2}}comparison_df=evaluator.compare_models(models_results)best_model,best_value=evaluator.find_best_model(models_results)