"""
CNN-BiGRU融合模型
结合CNN特征提取和BiGRU时序建模的深度学习模型
"""

import torch
import torch.nn as nn
import numpy as np
from typing import List, Optional, Dict, Any, Tuple
from .base_model import BaseTimeSeriesModel


class CNNBiGRUModel(BaseTimeSeriesModel):
    """CNN-BiGRU融合模型类，用于风电功率预测"""

    def __init__(self,
                 sequence_length: int,
                 n_features: Optional[int] = None,
                 cnn_filters: Optional[List[int]] = None,
                 cnn_kernel_sizes: Optional[List[int]] = None,
                 bigru_units: Optional[List[int]] = None,
                 dense_units: Optional[List[int]] = None,
                 dropout_rate: float = 0.2,
                 pool_size: int = 2):
        """
        初始化CNN-BiGRU融合模型

        Args:
            sequence_length: 时间序列长度
            n_features: 特征数量
            cnn_filters: CNN卷积层的滤波器数量列表
            cnn_kernel_sizes: CNN卷积核大小列表
            bigru_units: 双向GRU层的单元数列表
            dense_units: 全连接层的单元数列表
            dropout_rate: Dropout比例
            pool_size: 池化层大小
        """
        super().__init__('CNN-BiGRU', sequence_length, n_features)
        
        # 设置默认参数
        self.cnn_filters = cnn_filters or [32, 16]
        self.cnn_kernel_sizes = cnn_kernel_sizes or [3, 3]
        self.bigru_units = bigru_units or [64, 32]
        self.dense_units = dense_units or [32, 16]
        self.dropout_rate = dropout_rate
        self.pool_size = pool_size
        
        # 构建网络层
        if self.n_features is not None:
            self._build_layers()

    def _build_layers(self):
        """构建网络层"""
        print("构建CNN-BiGRU融合模型...")

        if self.n_features is None:
            raise ValueError("特征数量未设置，请在初始化时提供n_features参数")

        # 1. CNN特征提取层
        self.cnn_layers = nn.ModuleList()
        self.pool_layers = nn.ModuleList()
        self.cnn_batch_norms = nn.ModuleList()
        self.cnn_dropouts = nn.ModuleList()

        # 第一个CNN层
        self.cnn_layers.append(nn.Conv1d(
            in_channels=self.n_features,
            out_channels=self.cnn_filters[0],
            kernel_size=self.cnn_kernel_sizes[0],
            padding=self.cnn_kernel_sizes[0] // 2
        ))
        self.cnn_batch_norms.append(nn.BatchNorm1d(self.cnn_filters[0]))
        self.pool_layers.append(nn.MaxPool1d(kernel_size=self.pool_size))
        self.cnn_dropouts.append(nn.Dropout(self.dropout_rate))

        # 后续CNN层
        for i in range(1, len(self.cnn_filters)):
            self.cnn_layers.append(nn.Conv1d(
                in_channels=self.cnn_filters[i-1],
                out_channels=self.cnn_filters[i],
                kernel_size=self.cnn_kernel_sizes[i],
                padding=self.cnn_kernel_sizes[i] // 2
            ))
            self.cnn_batch_norms.append(nn.BatchNorm1d(self.cnn_filters[i]))
            self.pool_layers.append(nn.MaxPool1d(kernel_size=self.pool_size))
            self.cnn_dropouts.append(nn.Dropout(self.dropout_rate))

        # 计算CNN输出后的序列长度
        cnn_output_length = self.sequence_length
        for _ in range(len(self.cnn_filters)):
            cnn_output_length = cnn_output_length // self.pool_size

        # 2. 双向GRU层
        self.bigru_layers = nn.ModuleList()
        self.bigru_batch_norms = nn.ModuleList()
        self.bigru_dropouts = nn.ModuleList()

        # 第一个双向GRU层（输入来自CNN）
        self.bigru_layers.append(nn.GRU(
            input_size=self.cnn_filters[-1],  # CNN最后一层的输出通道数
            hidden_size=self.bigru_units[0],
            batch_first=True,
            bidirectional=True,
            dropout=self.dropout_rate if len(self.bigru_units) > 1 else 0
        ))
        self.bigru_batch_norms.append(nn.BatchNorm1d(self.bigru_units[0] * 2))  # 双向输出
        self.bigru_dropouts.append(nn.Dropout(self.dropout_rate))

        # 后续双向GRU层
        for i in range(1, len(self.bigru_units)):
            self.bigru_layers.append(nn.GRU(
                input_size=self.bigru_units[i-1] * 2,  # 双向，所以乘以2
                hidden_size=self.bigru_units[i],
                batch_first=True,
                bidirectional=True,
                dropout=self.dropout_rate if i < len(self.bigru_units) - 1 else 0
            ))
            self.bigru_batch_norms.append(nn.BatchNorm1d(self.bigru_units[i] * 2))  # 双向输出
            self.bigru_dropouts.append(nn.Dropout(self.dropout_rate))

        # 3. 全连接层
        self.dense_layers = nn.ModuleList()
        self.dense_dropouts = nn.ModuleList()

        # 第一个全连接层（输入来自BiGRU）
        bigru_output_size = self.bigru_units[-1] * 2  # 双向，所以乘以2
        self.dense_layers.append(nn.Linear(bigru_output_size, self.dense_units[0]))
        self.dense_dropouts.append(nn.Dropout(self.dropout_rate))

        # 后续全连接层
        for i in range(1, len(self.dense_units)):
            self.dense_layers.append(nn.Linear(self.dense_units[i-1], self.dense_units[i]))
            self.dense_dropouts.append(nn.Dropout(self.dropout_rate))

        # 4. 输出层
        self.output_layer = nn.Linear(self.dense_units[-1], 1)

        print(f"CNN-BiGRU模型构建完成:")
        print(f"  CNN层数: {len(self.cnn_filters)}")
        print(f"  CNN滤波器: {self.cnn_filters}")
        print(f"  BiGRU层数: {len(self.bigru_units)}")
        print(f"  BiGRU单元: {self.bigru_units}")
        print(f"  全连接层: {self.dense_units}")
        print(f"  CNN输出序列长度: {cnn_output_length}")

    def apply_cnn_feature_extraction(self, x):
        """
        应用CNN进行特征提取

        Args:
            x: 输入数据 (batch_size, sequence_length, n_features)

        Returns:
            cnn_features: CNN提取的特征 (batch_size, sequence_length', cnn_filters[-1])
        """
        # 转换维度以适应Conv1d: (batch_size, n_features, sequence_length)
        x = x.transpose(1, 2)

        # 逐层应用CNN
        for i, (cnn_layer, batch_norm, pool_layer, dropout) in enumerate(
            zip(self.cnn_layers, self.cnn_batch_norms, self.pool_layers, self.cnn_dropouts)
        ):
            x = cnn_layer(x)
            x = batch_norm(x)
            x = torch.relu(x)
            x = pool_layer(x)
            x = dropout(x)

        # 转换回时序格式: (batch_size, sequence_length', n_features')
        x = x.transpose(1, 2)

        return x

    def apply_bigru_processing(self, x):
        """
        应用BiGRU进行时序建模

        Args:
            x: CNN特征 (batch_size, sequence_length', cnn_filters[-1])

        Returns:
            bigru_features: BiGRU处理后的特征 (batch_size, bigru_units[-1] * 2)
        """
        current_features = x

        # 逐层应用双向GRU
        for i, (bigru_layer, batch_norm, dropout) in enumerate(
            zip(self.bigru_layers, self.bigru_batch_norms, self.bigru_dropouts)
        ):
            gru_output, _ = bigru_layer(current_features)
            
            # 批标准化（在序列维度上）
            gru_output = batch_norm(gru_output.transpose(1, 2)).transpose(1, 2)
            gru_output = dropout(gru_output)
            
            current_features = gru_output

        # 取最后一个时间步的输出
        final_features = current_features[:, -1, :]

        return final_features

    def forward(self, x):
        """
        CNN-BiGRU融合架构的完整前向传播

        Args:
            x: 输入风电数据 (batch_size, sequence_length, n_features)

        Returns:
            prediction: 功率预测值 (batch_size, 1)
        """
        # 确保输入数据在正确的设备上
        device = next(self.parameters()).device
        if x.device != device:
            x = x.to(device)

        # 阶段1: CNN特征提取
        # 目的: 提取局部时序特征和空间特征
        cnn_features = self.apply_cnn_feature_extraction(x)

        # 阶段2: BiGRU时序建模
        # 目的: 建模长期时序依赖和双向上下文信息
        bigru_features = self.apply_bigru_processing(cnn_features)

        # 阶段3: 全连接层处理
        # 目的: 将高维特征映射到预测值
        current_features = bigru_features

        for dense_layer, dropout in zip(self.dense_layers, self.dense_dropouts):
            current_features = torch.relu(dense_layer(current_features))
            current_features = dropout(current_features)

        # 阶段4: 输出预测
        prediction = self.output_layer(current_features)

        return prediction

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'model_name': self.model_name,
            'model_type': 'CNN-BiGRU融合模型',
            'total_params': total_params,
            'trainable_params': trainable_params,
            'sequence_length': self.sequence_length,
            'n_features': self.n_features,
            'cnn_filters': self.cnn_filters,
            'cnn_kernel_sizes': self.cnn_kernel_sizes,
            'bigru_units': self.bigru_units,
            'dense_units': self.dense_units,
            'dropout_rate': self.dropout_rate,
            'pool_size': self.pool_size,
            'architecture': 'CNN特征提取 → BiGRU时序建模 → 全连接预测'
        }
