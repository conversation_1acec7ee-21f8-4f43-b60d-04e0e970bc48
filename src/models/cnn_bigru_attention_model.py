"""
CNN-BiGRU-Attention融合模型，用于风电功率预测
结合卷积神经网络的特征提取能力、双向GRU的时序建模能力和注意力机制
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Optional, Dict, Any
import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.models.base_model import BaseTimeSeriesModel
from src.utils.config import DATA_PATHS

# CNN-BIGRU-ATTENTION模型配置
CNN_BIGRU_ATTENTION_CONFIG = {
    'cnn_filters': [64, 32],        # CNN卷积层的滤波器数量
    'cnn_kernel_sizes': [3, 3],     # CNN卷积核大小
    'bigru_units': [128, 64],       # 双向GRU层的单元数
    'attention_units': 64,          # 注意力机制的隐藏单元数
    'dense_units': [64, 32],        # 全连接层的单元数
    'dropout_rate': 0.2,            # Dropout比例
    'pool_size': 2                  # 池化层大小
}

class AttentionLayer(nn.Module):
    """注意力机制层"""
    
    def __init__(self, hidden_size: int, attention_units: int):
        """
        初始化注意力层
        
        Args:
            hidden_size: GRU隐藏层大小
            attention_units: 注意力机制的隐藏单元数
        """
        super(AttentionLayer, self).__init__()
        self.hidden_size = hidden_size
        self.attention_units = attention_units
        
        # 注意力权重计算层
        self.W_a = nn.Linear(hidden_size, attention_units, bias=False)
        self.U_a = nn.Linear(attention_units, 1, bias=False)
        self.tanh = nn.Tanh()
        self.softmax = nn.Softmax(dim=1)
        
    def forward(self, gru_outputs):
        """
        前向传播
        
        Args:
            gru_outputs: GRU输出，形状为 (batch_size, seq_len, hidden_size)
            
        Returns:
            context_vector: 上下文向量，形状为 (batch_size, hidden_size)
            attention_weights: 注意力权重，形状为 (batch_size, seq_len)
        """
        # 计算注意力分数
        # gru_outputs: (batch_size, seq_len, hidden_size)
        # W_a(gru_outputs): (batch_size, seq_len, attention_units)
        attention_scores = self.W_a(gru_outputs)  # (batch_size, seq_len, attention_units)
        attention_scores = self.tanh(attention_scores)  # (batch_size, seq_len, attention_units)
        attention_scores = self.U_a(attention_scores).squeeze(-1)  # (batch_size, seq_len)
        
        # 计算注意力权重
        attention_weights = self.softmax(attention_scores)  # (batch_size, seq_len)
        
        # 计算上下文向量
        # attention_weights: (batch_size, seq_len) -> (batch_size, seq_len, 1)
        # gru_outputs: (batch_size, seq_len, hidden_size)
        context_vector = torch.sum(gru_outputs * attention_weights.unsqueeze(-1), dim=1)  # (batch_size, hidden_size)
        
        return context_vector, attention_weights

class CNNBiGRUAttentionModel(BaseTimeSeriesModel):
    """CNN-BiGRU-Attention融合模型类，用于风电功率预测"""

    def __init__(self,
                 sequence_length: int,
                 n_features: Optional[int] = None,
                 cnn_filters: Optional[List[int]] = None,
                 cnn_kernel_sizes: Optional[List[int]] = None,
                 bigru_units: Optional[List[int]] = None,
                 attention_units: int = 64,
                 dense_units: Optional[List[int]] = None,
                 dropout_rate: float = 0.2,
                 pool_size: int = 2):
        """
        初始化CNN-BiGRU-Attention融合模型

        Args:
            sequence_length: 时间序列长度
            n_features: 特征数量
            cnn_filters: CNN卷积层的滤波器数量列表
            cnn_kernel_sizes: CNN卷积核大小列表
            bigru_units: 双向GRU层的单元数列表
            attention_units: 注意力机制的隐藏单元数
            dense_units: 全连接层的单元数列表
            dropout_rate: Dropout比例
            pool_size: 池化层大小
        """
        super().__init__('CNN-BiGRU-Attention', sequence_length, n_features)

        # 使用默认配置或自定义配置
        self.cnn_filters = cnn_filters if cnn_filters is not None else CNN_BIGRU_ATTENTION_CONFIG['cnn_filters']
        self.cnn_kernel_sizes = cnn_kernel_sizes if cnn_kernel_sizes is not None else CNN_BIGRU_ATTENTION_CONFIG['cnn_kernel_sizes']
        self.bigru_units = bigru_units if bigru_units is not None else CNN_BIGRU_ATTENTION_CONFIG['bigru_units']
        self.attention_units = attention_units
        self.dense_units = dense_units if dense_units is not None else CNN_BIGRU_ATTENTION_CONFIG['dense_units']
        self.dropout_rate = dropout_rate
        self.pool_size = pool_size

        # 构建网络层
        if self.n_features is not None:
            self._build_layers()

    def _build_layers(self):
        """构建网络层"""
        print("构建CNN-BiGRU-Attention融合模型...")

        if self.n_features is None:
            raise ValueError("特征数量未设置，请在初始化时提供n_features参数")

        # CNN特征提取层
        self.cnn_layers = nn.ModuleList()
        self.cnn_batch_norms = nn.ModuleList()
        self.cnn_dropouts = nn.ModuleList()

        # 第一个CNN层
        self.cnn_layers.append(nn.Conv1d(
            in_channels=self.n_features,
            out_channels=self.cnn_filters[0],
            kernel_size=self.cnn_kernel_sizes[0],
            padding=self.cnn_kernel_sizes[0] // 2
        ))
        self.cnn_batch_norms.append(nn.BatchNorm1d(self.cnn_filters[0]))
        self.cnn_dropouts.append(nn.Dropout(self.dropout_rate))

        # 后续CNN层
        for i in range(1, len(self.cnn_filters)):
            self.cnn_layers.append(nn.Conv1d(
                in_channels=self.cnn_filters[i-1],
                out_channels=self.cnn_filters[i],
                kernel_size=self.cnn_kernel_sizes[i] if i < len(self.cnn_kernel_sizes) else self.cnn_kernel_sizes[-1],
                padding=(self.cnn_kernel_sizes[i] if i < len(self.cnn_kernel_sizes) else self.cnn_kernel_sizes[-1]) // 2
            ))
            self.cnn_batch_norms.append(nn.BatchNorm1d(self.cnn_filters[i]))
            self.cnn_dropouts.append(nn.Dropout(self.dropout_rate))

        # 池化层
        self.pool = nn.MaxPool1d(kernel_size=self.pool_size, stride=1, padding=self.pool_size // 2)

        # 计算CNN输出后的序列长度和特征数
        self.cnn_output_length = self.sequence_length
        self.cnn_output_features = self.cnn_filters[-1]

        # 双向GRU层（只使用一层，因为注意力机制会处理时序信息）
        self.bigru = nn.GRU(
            input_size=self.cnn_output_features,
            hidden_size=self.bigru_units[0],
            batch_first=True,
            bidirectional=True,
            dropout=self.dropout_rate
        )

        # 注意力机制层
        self.attention = AttentionLayer(
            hidden_size=self.bigru_units[0] * 2,  # 双向GRU输出
            attention_units=self.attention_units
        )

        # 全连接层
        self.dense_layers = nn.ModuleList()
        self.dense_dropouts = nn.ModuleList()

        # 第一个全连接层的输入维度是注意力机制的输出维度
        input_dim = self.bigru_units[0] * 2

        for units in self.dense_units:
            self.dense_layers.append(nn.Linear(input_dim, units))
            self.dense_dropouts.append(nn.Dropout(self.dropout_rate))
            input_dim = units

        # 输出层
        self.output_layer = nn.Linear(input_dim, 1)

        print(f"CNN-BiGRU-Attention融合模型构建完成:")
        print(f"  CNN滤波器: {self.cnn_filters}")
        print(f"  CNN卷积核: {self.cnn_kernel_sizes}")
        print(f"  双向GRU层: {self.bigru_units[0]}")
        print(f"  注意力单元: {self.attention_units}")
        print(f"  全连接层: {self.dense_units}")
        print(f"  Dropout率: {self.dropout_rate}")

    def forward(self, x):
        """
        前向传播

        Args:
            x: 输入数据，形状为 (batch_size, sequence_length, n_features)

        Returns:
            output: 输出预测值，形状为 (batch_size, 1)
            attention_weights: 注意力权重，形状为 (batch_size, sequence_length)
        """
        # CNN特征提取
        # 转换维度: (batch, seq, features) -> (batch, features, seq)
        x_cnn = x.transpose(1, 2)

        # CNN层
        for i, (cnn_layer, batch_norm, dropout) in enumerate(zip(self.cnn_layers, self.cnn_batch_norms, self.cnn_dropouts)):
            x_cnn = torch.relu(cnn_layer(x_cnn))
            x_cnn = batch_norm(x_cnn)
            x_cnn = dropout(x_cnn)

            # 在最后一个CNN层后应用池化
            if i == len(self.cnn_layers) - 1:
                x_cnn = self.pool(x_cnn)

        # 转换回GRU输入格式: (batch, features, seq) -> (batch, seq, features)
        x_gru = x_cnn.transpose(1, 2)

        # 双向GRU层
        gru_outputs, _ = self.bigru(x_gru)  # (batch_size, seq_len, hidden_size * 2)

        # 注意力机制
        context_vector, attention_weights = self.attention(gru_outputs)

        # 全连接层
        x = context_vector
        for dense_layer, dropout in zip(self.dense_layers, self.dense_dropouts):
            x = torch.relu(dense_layer(x))
            x = dropout(x)

        # 输出层
        output = self.output_layer(x)
        
        # 存储注意力权重用于可视化
        self.last_attention_weights = attention_weights.detach().cpu().numpy()
        
        return output

    def get_attention_weights(self):
        """
        获取最后一次前向传播的注意力权重
        
        Returns:
            注意力权重数组
        """
        if hasattr(self, 'last_attention_weights'):
            return self.last_attention_weights
        else:
            return None

    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息

        Returns:
            包含模型信息的字典
        """
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'model_name': self.model_name,
            'total_params': total_params,
            'trainable_params': trainable_params,
            'sequence_length': self.sequence_length,
            'n_features': self.n_features,
            'cnn_filters': self.cnn_filters,
            'cnn_kernel_sizes': self.cnn_kernel_sizes,
            'bigru_units': self.bigru_units,
            'attention_units': self.attention_units,
            'dense_units': self.dense_units,
            'dropout_rate': self.dropout_rate,
            'pool_size': self.pool_size
        }

    def set_n_features(self, n_features: int):
        """
        设置特征数量并构建网络层

        Args:
            n_features: 特征数量
        """
        self.n_features = n_features
        self._build_layers()

    def save_model(self, filepath: str):
        """
        保存模型

        Args:
            filepath: 保存路径
        """
        model_state = {
            'model_state_dict': self.state_dict(),
            'model_config': {
                'sequence_length': self.sequence_length,
                'n_features': self.n_features,
                'cnn_filters': self.cnn_filters,
                'cnn_kernel_sizes': self.cnn_kernel_sizes,
                'bigru_units': self.bigru_units,
                'attention_units': self.attention_units,
                'dense_units': self.dense_units,
                'dropout_rate': self.dropout_rate,
                'pool_size': self.pool_size
            },
            'model_info': self.get_model_info()
        }
        torch.save(model_state, filepath)
        print(f"CNN-BiGRU-Attention融合模型已保存至: {filepath}")

    @classmethod
    def load_model(cls, filepath: str, device: str = 'cpu'):
        """
        加载模型

        Args:
            filepath: 模型文件路径
            device: 设备

        Returns:
            加载的模型实例
        """
        checkpoint = torch.load(filepath, map_location=device)
        config = checkpoint['model_config']

        model = cls(
            sequence_length=config['sequence_length'],
            n_features=config['n_features'],
            cnn_filters=config['cnn_filters'],
            cnn_kernel_sizes=config['cnn_kernel_sizes'],
            bigru_units=config['bigru_units'],
            attention_units=config['attention_units'],
            dense_units=config['dense_units'],
            dropout_rate=config['dropout_rate'],
            pool_size=config['pool_size']
        )

        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(device)
        print(f"CNN-BiGRU-Attention融合模型已从 {filepath} 加载")

        return model
