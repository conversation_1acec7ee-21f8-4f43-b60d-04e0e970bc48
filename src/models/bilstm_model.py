"""双向LSTM模型，用于风电功率预测"""importtorchimporttorch.nnasnnimportnumpyasnpfromtypingimportList,Optional,Dict,Anyimportosimportsys#添加项目根目录到系统路径sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))fromsrc.models.base_modelimportBaseTimeSeriesModelfromsrc.utils.configimportDATA_PATHS#BILSTM模型配置BILSTM_CONFIG={'bilstm_units':[128,64],#双向LSTM层的单元数'dense_units':[64,32],#全连接层的单元数'dropout_rate':0.2,#Dropout比例'bidirectional':True#双向标志}classBiLSTMModel(BaseTimeSeriesModel):"""双向LSTM模型类，用于风电功率预测"""def__init__(self,sequence_length:int,n_features:Optional[int]=None,bilstm_units:Optional[List[int]]=None,dense_units:Optional[List[int]]=None,dropout_rate:float=0.2):"""初始化双向LSTM模型Args:sequence_length:时间序列长度n_features:特征数量bilstm_units:双向LSTM层的单元数列表dense_units:全连接层的单元数列表dropout_rate:Dropout比例"""super().__init__('BiLSTM',sequence_length,n_features)#使用默认配置或自定义配置self.bilstm_units=bilstm_unitsifbilstm_unitsisnotNoneelseBILSTM_CONFIG['bilstm_units']self.dense_units=dense_unitsifdense_unitsisnotNoneelseBILSTM_CONFIG['dense_units']self.dropout_rate=dropout_rate#构建网络层ifself.n_featuresisnotNone:self._build_layers()def_build_layers(self):"""构建网络层"""print("构建双向LSTM模型...")ifself.n_featuresisNone:raiseValueError("特征数量未设置，请在初始化时提供n_features参数")#双向LSTM层self.bilstm_layers=nn.ModuleList()self.batch_norms=nn.ModuleList()self.dropouts=nn.ModuleList()#第一个双向LSTM层self.bilstm_layers.append(nn.LSTM(input_size=self.n_features,hidden_size=self.bilstm_units[0],batch_first=True,bidirectional=True,#双向dropout=self.dropout_rateiflen(self.bilstm_units)>1else0))#批归一化和Dropout（双向输出的维度是hidden_size*2）self.batch_norms.append(nn.BatchNorm1d(self.bilstm_units[0]*2))self.dropouts.append(nn.Dropout(self.dropout_rate))#后续双向LSTM层foriinrange(1,len(self.bilstm_units)):self.bilstm_layers.append(nn.LSTM(input_size=self.bilstm_units[i-1]*2,#前一层双向输出hidden_size=self.bilstm_units[i],batch_first=True,bidirectional=True,dropout=self.dropout_rateifi<len(self.bilstm_units)-1else0))self.batch_norms.append(nn.BatchNorm1d(self.bilstm_units[i]*2))self.dropouts.append(nn.Dropout(self.dropout_rate))#全连接层self.dense_layers=nn.ModuleList()self.dense_dropouts=nn.ModuleList()#第一个全连接层的输入维度是最后一个双向LSTM层的输出维度input_dim=self.bilstm_units[-1]*2forunitsinself.dense_units:self.dense_layers.append(nn.Linear(input_dim,units))self.dense_dropouts.append(nn.Dropout(self.dropout_rate))input_dim=units#输出层self.output_layer=nn.Linear(input_dim,1)print(f"双向LSTM模型构建完成:")print(f"双向LSTM层:{self.bilstm_units}")print(f"全连接层:{self.dense_units}")print(f"Dropout率:{self.dropout_rate}")defforward(self,x):"""前向传播Args:x:输入数据，形状为(batch_size,sequence_length,n_features)Returns:输出预测值，形状为(batch_size,1)"""#双向LSTM层fori,(bilstm_layer,batch_norm,dropout)inenumerate(zip(self.bilstm_layers,self.batch_norms,self.dropouts)):ifi==0:#第一层直接使用输入bilstm_out,_=bilstm_layer(x)else:#后续层使用前一层的输出bilstm_out,_=bilstm_layer(bilstm_out)#如果不是最后一个双向LSTM层，应用批归一化和dropoutifi<len(self.bilstm_layers)-1:#批归一化需要调整维度:(batch,seq,hidden*2)->(batch,hidden*2,seq)->(batch,seq,hidden*2)bilstm_out=bilstm_out.transpose(1,2)bilstm_out=batch_norm(bilstm_out)bilstm_out=bilstm_out.transpose(1,2)bilstm_out=dropout(bilstm_out)#取最后一个时间步的输出last_output=bilstm_out[:,-1,:]#(batch_size,hidden_size*2)#全连接层x=last_outputfordense_layer,dropoutinzip(self.dense_layers,self.dense_dropouts):x=torch.relu(dense_layer(x))x=dropout(x)#输出层output=self.output_layer(x)returnoutputdefget_model_info(self)->Dict[str,Any]:"""获取模型信息Returns:包含模型信息的字典"""total_params=sum(p.numel()forpinself.parameters())trainable_params=sum(p.numel()forpinself.parameters()ifp.requires_grad)return{'model_name':self.model_name,'total_params':total_params,'trainable_params':trainable_params,'sequence_length':self.sequence_length,'n_features':self.n_features,'bilstm_units':self.bilstm_units,'dense_units':self.dense_units,'dropout_rate':self.dropout_rate}defset_n_features(self,n_features:int):"""设置特征数量并构建网络层Args:n_features:特征数量"""self.n_features=n_featuresself._build_layers()defsave_model(self,filepath:str):"""保存模型Args:filepath:保存路径"""model_state={'model_state_dict':self.state_dict(),'model_config':{'sequence_length':self.sequence_length,'n_features':self.n_features,'bilstm_units':self.bilstm_units,'dense_units':self.dense_units,'dropout_rate':self.dropout_rate},'model_info':self.get_model_info()}torch.save(model_state,filepath)print(f"双向LSTM模型已保存至:{filepath}")@classmethoddefload_model(cls,filepath:str,device:str='cpu'):"""加载模型Args:filepath:模型文件路径device:设备Returns:加载的模型实例"""checkpoint=torch.load(filepath,map_location=device)config=checkpoint['model_config']model=cls(sequence_length=config['sequence_length'],n_features=config['n_features'],bilstm_units=config['bilstm_units'],dense_units=config['dense_units'],dropout_rate=config['dropout_rate'])model.load_state_dict(checkpoint['model_state_dict'])model.to(device)print(f"双向LSTM模型已从{filepath}加载")returnmodel