"""基础模型类，定义了所有深度学习模型的通用接口和方法"""importosimportnumpyasnpimporttorchimporttorch.nnasnnimporttorch.optimasoptimfromtorch.utils.dataimportDataLoader,TensorDatasetimportjoblibfromtypingimportDict,List,Optional,Tuple,Union,AnyfromabcimportABC,abstractmethodimportsysfromtqdmimporttqdm#添加项目根目录到系统路径sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))fromsrc.utils.configimportDATA_PATHS,MODEL_CONFIGclassBaseTimeSeriesModel(nn.Module,ABC):"""时间序列预测的基础模型类"""def__init__(self,model_name:str,sequence_length:int,n_features:Optional[int]=None):"""初始化基础模型Args:model_name:模型名称sequence_length:时间序列长度n_features:特征数量"""super().__init__()self.model_name=model_nameself.sequence_length=sequence_lengthself.n_features=n_featuresself.device=MODEL_CONFIG['device']self.history={'train_loss':[],'val_loss':[],'train_mae':[],'val_mae':[]}self.scaler_X=Noneself.scaler_y=Noneself.best_val_loss=float('inf')self.early_stop_counter=0self.best_model_state=None@abstractmethoddefforward(self,x):"""前向传播，需要在子类中实现Args:x:输入数据Returns:模型输出"""passdeftrain_model(self,train_loader:DataLoader,val_loader:DataLoader,epochs:int=100,patience:int=15,learning_rate:float=0.001,save_best:bool=True,verbose:int=1)->Dict:"""训练模型Args:train_loader:训练数据加载器val_loader:验证数据加载器epochs:训练轮数patience:早停耐心值learning_rate:学习率save_best:是否保存最佳模型verbose:显示详细程度Returns:训练历史"""print(f"开始训练{self.model_name}模型...")#将模型移至指定设备self.to(self.device)#定义优化器和损失函数optimizer=optim.Adam(self.parameters(),lr=learning_rate)criterion=nn.MSELoss()mae_criterion=nn.L1Loss()#学习率调度器scheduler=optim.lr_scheduler.ReduceLROnPlateau(optimizer,mode='min',factor=0.5,patience=patience//2,min_lr=1e-7)#训练循环forepochinrange(epochs):#训练阶段self.train()train_loss=0.0train_mae=0.0train_batches=0train_progress=tqdm(train_loader,desc=f'Epoch{epoch+1}/{epochs}[Train]')ifverbose>0elsetrain_loaderforinputs,targetsintrain_progress:#将数据移至设备inputs=inputs.to(self.device)targets=targets.to(self.device)#梯度清零optimizer.zero_grad()#前向传播outputs=self(inputs)#计算损失loss=criterion(outputs,targets)mae=mae_criterion(outputs,targets)#反向传播和优化loss.backward()optimizer.step()#累计损失train_loss+=loss.item()train_mae+=mae.item()train_batches+=1#计算平均损失avg_train_loss=train_loss/train_batchesavg_train_mae=train_mae/train_batches#验证阶段self.eval()val_loss=0.0val_mae=0.0val_batches=0withtorch.no_grad():val_progress=tqdm(val_loader,desc=f'Epoch{epoch+1}/{epochs}[Val]')ifverbose>0elseval_loaderforinputs,targetsinval_progress:#将数据移至设备inputs=inputs.to(self.device)targets=targets.to(self.device)#前向传播outputs=self(inputs)#计算损失loss=criterion(outputs,targets)mae=mae_criterion(outputs,targets)#累计损失val_loss+=loss.item()val_mae+=mae.item()val_batches+=1#计算平均损失avg_val_loss=val_loss/val_batchesavg_val_mae=val_mae/val_batches#更新学习率scheduler.step(avg_val_loss)#记录历史self.history['train_loss'].append(avg_train_loss)self.history['val_loss'].append(avg_val_loss)self.history['train_mae'].append(avg_train_mae)self.history['val_mae'].append(avg_val_mae)#打印进度ifverbose>0:print(f"Epoch{epoch+1}/{epochs}-"f"loss:{avg_train_loss:.4f}-"f"mae:{avg_train_mae:.4f}-"f"val_loss:{avg_val_loss:.4f}-"f"val_mae:{avg_val_mae:.4f}-"f"lr:{optimizer.param_groups[0]['lr']:.6f}")#保存最佳模型ifavg_val_loss<self.best_val_loss:self.best_val_loss=avg_val_lossself.early_stop_counter=0ifsave_best:self.best_model_state=self.state_dict().copy()ifverbose>0:print(f"Epoch{epoch+1}:val_lossimprovedto{self.best_val_loss:.6f}")else:self.early_stop_counter+=1ifverbose>0:print(f"Epoch{epoch+1}:val_lossdidnotimprovefrom{self.best_val_loss:.6f}")#早停ifself.early_stop_counter>=patience:ifverbose>0:print(f"Earlystoppingtriggeredafter{epoch+1}epochs")break#恢复最佳模型ifsave_bestandself.best_model_stateisnotNone:self.load_state_dict(self.best_model_state)print(f"恢复最佳模型权重")print(f"{self.model_name}模型训练完成！")returnself.historydefpredict(self,X:np.ndarray,denormalize:bool=True)->np.ndarray:"""模型预测Args:X:输入特征denormalize:是否反标准化预测结果Returns:预测结果"""self.eval()#转换为PyTorch张量X_tensor=torch.FloatTensor(X).to(self.device)withtorch.no_grad():predictions_scaled=self(X_tensor).cpu().numpy()ifdenormalizeandself.scaler_yisnotNone:predictions=self.scaler_y.inverse_transform(predictions_scaled.reshape(-1,1))returnpredictions.flatten()returnpredictions_scaled.flatten()defevaluate(self,X_train:np.ndarray,y_train:np.ndarray,X_val:np.ndarray,y_val:np.ndarray,X_test:np.ndarray,y_test:np.ndarray,use_normalized_metrics:bool=True)->Dict:"""评估模型性能Args:X_train:训练特征y_train:训练标签X_val:验证特征y_val:验证标签X_test:测试特征y_test:测试标签use_normalized_metrics:是否使用标准化后的指标（默认True，显示零点几的数值）Returns:包含评估结果的字典"""fromsklearn.metricsimportmean_absolute_error,mean_squared_error,r2_scoreprint(f"评估{self.model_name}模型性能...")ifuse_normalized_metrics:print("使用标准化后的指标（0-1范围内的数值）")#使用标准化后的预测和真实值y_train_pred=self.predict(X_train,denormalize=False)y_val_pred=self.predict(X_val,denormalize=False)y_test_pred=self.predict(X_test,denormalize=False)y_train_true=y_trainy_val_true=y_valy_test_true=y_testelse:print("使用原始功率值的指标")#使用反标准化后的预测和真实值y_train_pred=self.predict(X_train,denormalize=True)y_val_pred=self.predict(X_val,denormalize=True)y_test_pred=self.predict(X_test,denormalize=True)#反标准化真实值ifself.scaler_yisnotNone:y_train_true=self.scaler_y.inverse_transform(y_train.reshape(-1,1)).flatten()y_val_true=self.scaler_y.inverse_transform(y_val.reshape(-1,1)).flatten()y_test_true=self.scaler_y.inverse_transform(y_test.reshape(-1,1)).flatten()else:y_train_true=y_trainy_val_true=y_valy_test_true=y_test#计算指标defcalculate_metrics(y_true,y_pred,dataset_name):mae=mean_absolute_error(y_true,y_pred)mse=mean_squared_error(y_true,y_pred)rmse=np.sqrt(mse)r2=r2_score(y_true,y_pred)#计算MAPE(MeanAbsolutePercentageError)#避免除以零mask=y_true!=0mape=np.mean(np.abs((y_true[mask]-y_pred[mask])/y_true[mask]))*100print(f"\n{dataset_name}集性能:")print(f"MAE:{mae:.4f}")print(f"MSE:{mse:.4f}")print(f"RMSE:{rmse:.4f}")print(f"R²:{r2:.4f}")print(f"MAPE:{mape:.4f}%")return{'MAE':mae,'MSE':mse,'RMSE':rmse,'R2':r2,'MAPE':mape}train_metrics=calculate_metrics(y_train_true,y_train_pred,"训练")val_metrics=calculate_metrics(y_val_true,y_val_pred,"验证")test_metrics=calculate_metrics(y_test_true,y_test_pred,"测试")return{'train':train_metrics,'val':val_metrics,'test':test_metrics,'predictions':{'train':(y_train_true,y_train_pred),'val':(y_val_true,y_val_pred),'test':(y_test_true,y_test_pred)}}defsave_model(self,model_path:Optional[str]=None,scaler_path:Optional[str]=None)->Tuple[str,str]:"""保存模型和标准化器Args:model_path:模型保存路径，如果为None则使用默认路径scaler_path:标准化器保存路径，如果为None则使用默认路径Returns:model_path:模型保存路径scaler_path:标准化器保存路径"""#如果提供了完整路径，直接使用ifmodel_pathisnotNone:#确保目录存在model_dir=os.path.dirname(model_path)ifmodel_dir:os.makedirs(model_dir,exist_ok=True)#改进的保存方法，处理路径编码和CUDA问题try:#确保模型在CPU上以避免CUDA问题original_device=next(self.parameters()).deviceself.cpu()#保存模型状态torch.save(self.state_dict(),model_path)print(f"模型已保存到:{model_path}")#恢复原设备self.to(original_device)exceptExceptionase:print(f"保存模型时出错:{e}")#尝试备用方法try:backup_path=model_path.replace('.pth','_backup.pth')torch.save(self.state_dict(),backup_path)print(f"使用备用路径保存:{backup_path}")exceptExceptionasbackup_error:print(f"备用保存也失败:{backup_error}")raisee#如果没有提供scaler_path，在同一目录下创建ifscaler_pathisNone:scaler_path=os.path.join(model_dir,f'{self.model_name.lower()}_scalers.pkl')else:#使用默认路径model_dir=os.path.join(DATA_PATHS['models'],self.model_name.lower())os.makedirs(model_dir,exist_ok=True)model_path=os.path.join(model_dir,f'{self.model_name.lower()}_model.pth')torch.save(self.state_dict(),model_path)print(f"模型已保存到:{model_path}")ifscaler_pathisNone:scaler_path=os.path.join(model_dir,f'{self.model_name.lower()}_scalers.pkl')#保存标准化器和配置scaler_dir=os.path.dirname(scaler_path)ifscaler_dir:os.makedirs(scaler_dir,exist_ok=True)joblib.dump({'scaler_X':self.scaler_X,'scaler_y':self.scaler_y,'sequence_length':self.sequence_length,'n_features':self.n_features,'model_name':self.model_name},scaler_path)print(f"标准化器和配置已保存到:{scaler_path}")returnmodel_path,scaler_pathdefload_model(self,model_path:Optional[str]=None,scaler_path:Optional[str]=None)->None:"""加载模型和标准化器Args:model_path:模型加载路径，如果为None则使用默认路径scaler_path:标准化器加载路径，如果为None则使用默认路径"""#确定模型目录model_dir=os.path.join(DATA_PATHS['models'],self.model_name.lower())#加载模型ifmodel_pathisNone:model_path=os.path.join(model_dir,f'{self.model_name.lower()}_model.pth')self.load_state_dict(torch.load(model_path,map_location=self.device))print(f"模型已从{model_path}加载")#加载标准化器和配置ifscaler_pathisNone:scaler_path=os.path.join(model_dir,f'{self.model_name.lower()}_scalers.pkl')scalers=joblib.load(scaler_path)self.scaler_X=scalers['scaler_X']self.scaler_y=scalers['scaler_y']self.sequence_length=scalers['sequence_length']self.n_features=scalers['n_features']print(f"标准化器和配置已从{scaler_path}加载")defset_scalers(self,scaler_X,scaler_y)->None:"""设置标准化器Args:scaler_X:特征标准化器scaler_y:目标变量标准化器"""self.scaler_X=scaler_Xself.scaler_y=scaler_y