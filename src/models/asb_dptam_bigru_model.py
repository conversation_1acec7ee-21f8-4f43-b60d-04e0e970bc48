"""ASB-DPTAM-BiGRU融合模型，用于风电功率预测结合ASB的自适应频谱滤波、DPTAM的时序注意力机制和BiGRU的双向特征提取能力"""importtorchimporttorch.nnasnnimporttorch.nn.functionalasFimportnumpyasnpfromtypingimportList,Optional,Dict,Anyimportosimportsys#添加项目根目录到系统路径sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))fromsrc.models.base_modelimportBaseTimeSeriesModelfromsrc.models.dptam_bigru_modelimportAdaptedDPTAMfromsrc.utils.configimportDATA_PATHSclassAdaptive_Spectral_Block(nn.Module):"""自适应频谱块(ASB)利用傅里叶分析增强特征表示，捕获长期和短期交互，通过自适应阈值减轻噪声"""def__init__(self,dim,adaptive_filter=True):super().__init__()self.complex_weight_high=nn.Parameter(torch.randn(dim,2,dtype=torch.float32)*0.02)self.complex_weight=nn.Parameter(torch.randn(dim,2,dtype=torch.float32)*0.02)#使用PyTorch内置的正态分布初始化nn.init.trunc_normal_(self.complex_weight_high,std=0.02)nn.init.trunc_normal_(self.complex_weight,std=0.02)self.threshold_param=nn.Parameter(torch.rand(1))self.adaptive_filter=adaptive_filterdefcreate_adaptive_high_freq_mask(self,x_fft):"""创建自适应高频掩码"""B,_,_=x_fft.shape#计算频域中的能量energy=torch.abs(x_fft).pow(2).sum(dim=-1)#将能量展平，并计算中位数flat_energy=energy.view(B,-1)median_energy=flat_energy.median(dim=1,keepdim=True)[0]median_energy=median_energy.view(B,1)#归一化能量normalized_energy=energy/(median_energy+1e-6)adaptive_mask=((normalized_energy>self.threshold_param).float()-self.threshold_param).detach()+self.threshold_paramadaptive_mask=adaptive_mask.unsqueeze(-1)returnadaptive_maskdefforward(self,x_in):"""ASB前向传播Args:x_in:输入数据(B,N,C)Returns:频域处理后的输出(B,N,C)"""B,N,C=x_in.shapedtype=x_in.dtypex=x_in.to(torch.float32)#沿时间维度应用FFTx_fft=torch.fft.rfft(x,dim=1,norm='ortho')weight=torch.view_as_complex(self.complex_weight)x_weighted=x_fft*weightifself.adaptive_filter:#自适应高频掩码freq_mask=self.create_adaptive_high_freq_mask(x_fft)x_masked=x_fft*freq_mask.to(x.device)weight_high=torch.view_as_complex(self.complex_weight_high)x_weighted2=x_masked*weight_highx_weighted+=x_weighted2#应用逆FFTx=torch.fft.irfft(x_weighted,n=N,dim=1,norm='ortho')x=x.to(dtype)x=x.view(B,N,C)returnxclassASBDPTAMBiGRUModel(BaseTimeSeriesModel):"""ASB-DPTAM-BiGRU融合模型类，用于风电功率预测"""def__init__(self,sequence_length:int,n_features:Optional[int]=None,n_segment:Optional[int]=None,dptam_kernel_size:int=3,bigru_units:Optional[List[int]]=None,dense_units:Optional[List[int]]=None,dropout_rate:float=0.2,asb_adaptive_filter:bool=True):"""初始化ASB-DPTAM-BiGRU融合模型Args:sequence_length:时间序列长度n_features:特征数量n_segment:DPTAM分段数dptam_kernel_size:DPTAM卷积核大小bigru_units:双向GRU层的单元数列表dense_units:全连接层的单元数列表dropout_rate:Dropout比例asb_adaptive_filter:ASB是否启用自适应滤波"""super().__init__('ASB-DPTAM-BiGRU',sequence_length,n_features)#使用默认配置或自定义配置fromsrc.utils.configimportDPTAM_BIGRU_CONFIGself.n_segment=n_segmentifn_segmentisnotNoneelseDPTAM_BIGRU_CONFIG['n_segment']self.dptam_kernel_size=dptam_kernel_sizeself.bigru_units=bigru_unitsifbigru_unitsisnotNoneelseDPTAM_BIGRU_CONFIG['bigru_units']self.dense_units=dense_unitsifdense_unitsisnotNoneelseDPTAM_BIGRU_CONFIG['dense_units']self.dropout_rate=dropout_rateself.asb_adaptive_filter=asb_adaptive_filter#验证分段配置ifsequence_length%self.n_segment!=0:raiseValueError(f"序列长度{sequence_length}必须能被分段数{self.n_segment}整除")self.segment_length=sequence_length//self.n_segment#构建网络层ifself.n_featuresisnotNone:self._build_layers()def_build_layers(self):"""构建网络层"""print("构建ASB-DPTAM-BiGRU融合模型...")ifself.n_featuresisNone:raiseValueError("特征数量未设置，请在初始化时提供n_features参数")#1.ASB自适应频谱块self.asb=Adaptive_Spectral_Block(dim=self.n_features,adaptive_filter=self.asb_adaptive_filter)#2.DPTAM时序注意力模块self.dptam=AdaptedDPTAM(in_channels=self.n_features,n_segment=self.n_segment,kernel_size=self.dptam_kernel_size)#3.残差连接权重参数（可学习）self.residual_weight_1=nn.Parameter(torch.tensor(0.1))#ASB残差权重self.residual_weight_2=nn.Parameter(torch.tensor(0.1))#DPTAM残差权重#4.特征融合层self.asb_fusion=nn.Sequential(nn.Linear(self.n_features*2,self.n_features),nn.LayerNorm(self.n_features),nn.GELU(),nn.Dropout(0.1))self.dptam_fusion=nn.Sequential(nn.Linear(self.n_features*2,self.n_features),nn.LayerNorm(self.n_features),nn.GELU(),nn.Dropout(0.1))#3.双向GRU层self.bigru_layers=nn.ModuleList()self.batch_norms=nn.ModuleList()self.dropouts=nn.ModuleList()#第一个双向GRU层self.bigru_layers.append(nn.GRU(input_size=self.n_features,hidden_size=self.bigru_units[0],batch_first=True,bidirectional=True,dropout=self.dropout_rateiflen(self.bigru_units)>1else0))self.batch_norms.append(nn.BatchNorm1d(self.bigru_units[0]*2))self.dropouts.append(nn.Dropout(self.dropout_rate))#后续双向GRU层foriinrange(1,len(self.bigru_units)):self.bigru_layers.append(nn.GRU(input_size=self.bigru_units[i-1]*2,hidden_size=self.bigru_units[i],batch_first=True,bidirectional=True,dropout=self.dropout_rateifi<len(self.bigru_units)-1else0))self.batch_norms.append(nn.BatchNorm1d(self.bigru_units[i]*2))self.dropouts.append(nn.Dropout(self.dropout_rate))#4.全连接层self.dense_layers=nn.ModuleList()self.dense_dropouts=nn.ModuleList()input_dim=self.bigru_units[-1]*2forunitsinself.dense_units:self.dense_layers.append(nn.Linear(input_dim,units))self.dense_dropouts.append(nn.Dropout(self.dropout_rate))input_dim=units#5.输出层self.output_layer=nn.Linear(input_dim,1)print(f"改进版ASB-DPTAM-BiGRU融合模型构建完成:")print(f"ASB自适应滤波:{self.asb_adaptive_filter}")print(f"DPTAM分段:{self.n_segment}")print(f"双向GRU层:{self.bigru_units}")print(f"全连接层:{self.dense_units}")print(f"Dropout率:{self.dropout_rate}")print(f"新增功能:")print(f"-残差连接权重:α₁={self.residual_weight_1.item():.3f},α₂={self.residual_weight_2.item():.3f}")print(f"-ASB特征融合层:{self.n_features*2}→{self.n_features}")print(f"-DPTAM特征融合层:{self.n_features*2}→{self.n_features}")defadapt_input_for_dptam(self,x):"""将风电时序数据适配为DPTAM输入格式"""batch_size,sequence_length,n_features=x.shape#重塑为分段格式x_reshaped=x.view(batch_size,self.n_segment,self.segment_length,n_features)x_reordered=x_reshaped.permute(0,1,3,2).contiguous()#重塑为DPTAM期望的格式nt=batch_size*self.n_segmentx_adapted=x_reordered.view(nt,n_features,self.segment_length,1)returnx_adapteddefrestore_from_dptam(self,x_attended,original_batch_size):"""将DPTAM输出恢复为时序格式"""nt,n_features,segment_length,_=x_attended.shape#重塑回分段格式x_reshaped=x_attended.view(original_batch_size,self.n_segment,n_features,segment_length)x_reordered=x_reshaped.permute(0,1,3,2).contiguous()#恢复为原始时序格式x_output=x_reordered.view(original_batch_size,self.sequence_length,n_features)returnx_outputdefapply_asb_processing(self,x):"""应用ASB自适应频谱处理Args:x:输入数据(batch_size,sequence_length,n_features)Returns:asb_features:ASB处理后的特征(batch_size,sequence_length,n_features)"""returnself.asb(x)defapply_dptam_attention(self,x):"""应用DPTAM时序注意力机制"""batch_size=x.shape[0]#维度适配x_adapted=self.adapt_input_for_dptam(x)#DPTAM前向传播x_attended=self.dptam(x_adapted)#恢复原始维度结构x_output=self.restore_from_dptam(x_attended,batch_size)returnx_outputdefapply_bigru_processing(self,x):"""应用BiGRU进行双向时序特征提取"""bigru_out=xfori,(bigru_layer,batch_norm,dropout)inenumerate(zip(self.bigru_layers,self.batch_norms,self.dropouts)):bigru_out,_=bigru_layer(bigru_out)ifi<len(self.bigru_layers)-1:bigru_out=bigru_out.transpose(1,2)bigru_out=batch_norm(bigru_out)bigru_out=bigru_out.transpose(1,2)bigru_out=dropout(bigru_out)#使用最后一个时间步的输出final_output=bigru_out[:,-1,:]returnfinal_outputdefforward(self,x):"""改进版ASB-DPTAM-BiGRU架构的完整前向传播改进点:1.添加残差连接改善梯度流2.特征融合层减少信息丢失3.自适应权重学习最优组合处理流程:1.ASB频域增强+残差连接2.特征融合（ASB+原始）3.DPTAM时序注意力+残差连接4.特征融合（DPTAM+前一阶段）5.BiGRU双向特征提取6.全连接层处理和输出Args:x:输入风电数据(batch_size,sequence_length,n_features)Returns:prediction:功率预测值(batch_size,1)"""#确保输入数据在正确的设备上device=next(self.parameters()).deviceifx.device!=device:x=x.to(device)#保存原始输入用于残差连接x_original=x#===阶段1:ASB处理+特征融合+残差连接===x_asb=self.apply_asb_processing(x)#特征融合：组合ASB特征和原始特征x_asb_concat=torch.cat([x_asb,x_original],dim=-1)#(B,T,2*F)x_asb_fused=self.asb_fusion(x_asb_concat)#(B,T,F)#残差连接：ASB融合特征+加权原始输入x_asb_final=x_asb_fused+self.residual_weight_1*x_original#===阶段2:DPTAM处理+特征融合+残差连接===x_dptam=self.apply_dptam_attention(x_asb_final)#特征融合：组合DPTAM特征和ASB最终特征x_dptam_concat=torch.cat([x_dptam,x_asb_final],dim=-1)#(B,T,2*F)x_dptam_fused=self.dptam_fusion(x_dptam_concat)#(B,T,F)#残差连接：DPTAM融合特征+加权ASB特征x_dptam_final=x_dptam_fused+self.residual_weight_2*x_asb_final#===阶段3:BiGRU双向特征提取===bigru_features=self.apply_bigru_processing(x_dptam_final)#===阶段4:全连接层处理===current_features=bigru_featuresfordense_layer,dropoutinzip(self.dense_layers,self.dense_dropouts):current_features=torch.relu(dense_layer(current_features))current_features=dropout(current_features)#===阶段5:输出预测===prediction=self.output_layer(current_features)returnpredictiondefget_model_info(self)->Dict[str,Any]:"""获取模型信息"""total_params=sum(p.numel()forpinself.parameters())ifhasattr(self,'bigru_layers')elseNonereturn{'model_name':self.model_name,'sequence_length':self.sequence_length,'n_features':self.n_features,'fusion_strategy':'ASB→DPTAM→BiGRU(改进版串联+残差+融合)','asb_adaptive_filter':self.asb_adaptive_filter,'n_segment':self.n_segment,'segment_length':self.segment_length,'dptam_kernel_size':self.dptam_kernel_size,'bigru_units':self.bigru_units,'dense_units':self.dense_units,'dropout_rate':self.dropout_rate,'bidirectional':True,'residual_weights':{'asb_residual':self.residual_weight_1.item()ifhasattr(self,'residual_weight_1')elseNone,'dptam_residual':self.residual_weight_2.item()ifhasattr(self,'residual_weight_2')elseNone},'feature_fusion':{'asb_fusion_layers':'Linear+LayerNorm+GELU+Dropout','dptam_fusion_layers':'Linear+LayerNorm+GELU+Dropout'},'total_params':total_params}#ASB-DPTAM-BiGRU融合模型配置ASB_DPTAM_BIGRU_CONFIG={'name':'ASB-DPTAM-BiGRU','fusion_strategy':'asb_dptam_serial',#ASB+DPTAM串行融合策略#ASB配置'asb_adaptive_filter':True,#DPTAM配置'n_segment':6,'dptam_kernel_size':3,#BiGRU配置'bigru_units':[64,32],'dense_units':[32,16],'dropout_rate':0.3,'bidirectional':True,}