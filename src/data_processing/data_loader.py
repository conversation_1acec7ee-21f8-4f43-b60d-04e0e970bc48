"""数据加载模块，负责从文件中读取数据"""importosimportpandasaspdimportnumpyasnpimportmatplotlib.pyplotaspltimportseabornassnsfromtypingimportTuple,Dict,List,Optional,Unionimportsys#添加项目根目录到系统路径sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))fromsrc.utils.configimportget_current_paths,DATASET_CONFIG,ACADEMIC_COLORS,setup_matplotlibfromsrc.utils.data_converterimportDataFormatConverterclassDataLoader:"""数据加载类，负责加载和分析原始数据"""def__init__(self,data_file:str=None,auto_convert:bool=True):"""初始化数据加载器Args:data_file:数据文件名（如果为None，使用config.py中的配置）auto_convert:是否自动转换非CSV文件为CSV格式"""ifdata_fileisNone:data_file=DATASET_CONFIG['data_file']self.original_data_file=os.path.join(get_current_paths()['raw_data'],data_file)self.data_file=self.original_data_file#实际使用的文件路径（可能是转换后的CSV）self.df=Noneself.auto_convert=auto_convertself.converter=DataFormatConverter()self.was_converted=Falseprint(f"数据加载器初始化:")print(f"数据文件:{data_file}")print(f"完整路径:{self.original_data_file}")print(f"自动转换:{auto_convert}")print(f"目标列:{DATASET_CONFIG['target_column']}")print(f"数据集描述:{DATASET_CONFIG['data_description']}")defload_data(self)->pd.DataFrame:"""加载数据，自动检查格式并转换为CSV（如果需要）Returns:加载的数据框"""print(f"\n开始数据加载流程...")print(f"原始文件:{os.path.basename(self.original_data_file)}")try:#检查原始文件是否存在ifnotos.path.exists(self.original_data_file):raiseFileNotFoundError(f"数据文件不存在:{self.original_data_file}")#自动格式转换（如果启用）ifself.auto_convert:print(f"\n检查文件格式...")file_info=self.converter.get_file_info(self.original_data_file)print(f"文件格式:{file_info['extension']}")print(f"文件大小:{file_info['size_mb']:.2f}MB")print(f"是否为CSV:{file_info['is_csv']}")ifnotfile_info['is_csv']:print(f"\n文件不是CSV格式，开始自动转换...")self.data_file,self.was_converted=self.converter.check_and_convert_to_csv(self.original_data_file,force_convert=False)ifself.was_converted:print(f"格式转换完成，使用CSV文件:{os.path.basename(self.data_file)}")else:print(f"使用现有CSV文件:{os.path.basename(self.data_file)}")else:print(f"文件已经是CSV格式")self.data_file=self.original_data_file#加载CSV数据print(f"\n📖正在加载数据:{os.path.basename(self.data_file)}")#尝试不同的编码方式读取CSV文件encodings=['utf-8','gbk','gb2312','latin-1','cp1252']forencodinginencodings:try:self.df=pd.read_csv(self.data_file,encoding=encoding)print(f"使用编码'{encoding}'成功读取")breakexceptUnicodeDecodeError:continueelse:raiseValueError(f"无法使用任何编码读取CSV文件:{self.data_file}")#转换日期列（使用配置中的列名）datetime_col=DATASET_CONFIG['datetime_column']ifdatetime_colanddatetime_colinself.df.columns:self.df[datetime_col]=pd.to_datetime(self.df[datetime_col])print(f"时间列'{datetime_col}'已转换为datetime格式")print(f"\n数据加载完成:")print(f"数据形状:{self.df.shape}")print(f"数据列数:{len(self.df.columns)}")print(f"内存使用:{self.df.memory_usage(deep=True).sum()/1024/1024:.2f}MB")returnself.dfexceptExceptionase:print(f"\n数据加载失败:{str(e)}")print(f"文件路径:{self.data_file}")print(f"文件是否存在:{os.path.exists(self.data_file)}")raisedefget_conversion_info(self)->Dict:"""获取文件转换信息Returns:dict:转换信息字典"""return{'original_file':self.original_data_file,'current_file':self.data_file,'was_converted':self.was_converted,'auto_convert_enabled':self.auto_convert,'original_exists':os.path.exists(self.original_data_file),'current_exists':os.path.exists(self.data_file)ifhasattr(self,'data_file')elseFalse}defanalyze_data(self)->Dict:"""分析数据，生成基本统计信息Returns:包含数据分析结果的字典"""ifself.dfisNone:self.load_data()print("正在分析数据...")#基本信息info={'shape':self.df.shape,'columns':self.df.columns.tolist(),'dtypes':self.df.dtypes,'missing_values':self.df.isnull().sum(),'stats':self.df.describe()}#打印基本信息print(f"数据形状:{info['shape']}")print(f"数据列:{info['columns']}")print("\n数据类型:")print(info['dtypes'])#检查缺失值missing_values=info['missing_values']ifmissing_values.sum()>0:print("\n缺失值统计:")print(missing_values[missing_values>0])else:print("\n数据中没有缺失值")#基本统计信息print("\n数据统计信息:")print(info['stats'])returninfodefvisualize_data(self,save_path:Optional[str]=None)->None:"""数据可视化分析Args:save_path:图像保存路径，如果为None则不保存"""ifself.dfisNone:self.load_data()print("正在生成数据可视化...")#设置学术风格setup_matplotlib()#创建图形fig=plt.figure(figsize=(15,12))#1.功率时间序列图ax1=fig.add_subplot(3,2,1)datetime_col=DATASET_CONFIG['datetime_column']target_col=DATASET_CONFIG['target_column']ax1.plot(self.df[datetime_col][:1000],self.df[target_col][:1000],color=ACADEMIC_COLORS['primary'],linewidth=1.5)ax1.set_title('风电功率时间序列(前1000个数据点)')ax1.set_xlabel('时间')ax1.set_ylabel('功率(MW)')ax1.tick_params(axis='x',rotation=45)#2.功率分布直方图ax2=fig.add_subplot(3,2,2)sns.histplot(self.df[target_col],bins=50,color=ACADEMIC_COLORS['primary'],kde=True,ax=ax2,edgecolor='white',alpha=0.7)ax2.set_title('风电功率分布')ax2.set_xlabel('功率(MW)')ax2.set_ylabel('频次')#3.风速与功率的关系ax3=fig.add_subplot(3,2,3)wind_speed_col='Windspeed-attheheightofwheelhub(m/s)'ax3.scatter(self.df[wind_speed_col][:5000],self.df[target_col][:5000],alpha=0.5,color=ACADEMIC_COLORS['secondary'],s=15)ax3.set_title('轮毂高度风速vs功率')ax3.set_xlabel('轮毂高度风速(m/s)')ax3.set_ylabel('功率(MW)')#4.温度与功率的关系ax4=fig.add_subplot(3,2,4)temp_col='Airtemperature(°C)'ax4.scatter(self.df[temp_col][:5000],self.df[target_col][:5000],alpha=0.5,color=ACADEMIC_COLORS['accent'],s=15)ax4.set_title('气温vs功率')ax4.set_xlabel('气温(°C)')ax4.set_ylabel('功率(MW)')#5.相关性热力图ax5=fig.add_subplot(3,2,5)numeric_cols=self.df.select_dtypes(include=[np.number]).columnscorrelation_matrix=self.df[numeric_cols].corr()sns.heatmap(correlation_matrix,annot=False,cmap='coolwarm',center=0,ax=ax5)ax5.set_title('特征相关性热力图')#6.功率的月度变化ax6=fig.add_subplot(3,2,6)self.df['month']=self.df[datetime_col].dt.monthmonthly_power=self.df.groupby('month')[target_col].mean()ax6.plot(monthly_power.index,monthly_power.values,marker='o',color=ACADEMIC_COLORS['info'],linewidth=2)ax6.set_title('月度平均功率变化')ax6.set_xlabel('月份')ax6.set_ylabel('平均功率(MW)')ax6.set_xticks(range(1,13))#调整布局plt.tight_layout()#保存图像ifsave_path:save_file=os.path.join(save_path,'data_analysis.png')plt.savefig(save_file,dpi=300,bbox_inches='tight')print(f"数据分析图表已保存到:{save_file}")plt.show()#额外的相关性分析plt.figure(figsize=(12,10))#选择与功率相关的主要特征power_corr=correlation_matrix[target_col].sort_values(ascending=False)top_features=power_corr.index[:10]#取相关性最高的10个特征#绘制相关性条形图plt.barh(top_features,power_corr[top_features],color=ACADEMIC_COLORS['primary'])plt.title('与功率相关性最高的特征')plt.xlabel('相关系数')plt.grid(axis='x',alpha=0.3)#保存图像ifsave_path:save_file=os.path.join(save_path,'power_correlation.png')plt.savefig(save_file,dpi=300,bbox_inches='tight')print(f"功率相关性图表已保存到:{save_file}")plt.show()#测试代码if__name__=="__main__":loader=DataLoader()df=loader.load_data()loader.analyze_data()loader.visualize_data(save_path=get_current_paths()['figures'])