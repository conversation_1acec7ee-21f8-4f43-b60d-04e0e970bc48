#!/usr/bin/env python3
"""
测试CNN-BiGRU模型添加验证
"""

import sys
import os

# 设置环境变量以避免OpenMP冲突
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_import():
    """测试配置文件导入"""
    try:
        from src.utils.config import (
            COMPARISON_EXPERIMENT_CONFIG,
            CNN_BIGRU_CONFIG,
            BIGRU_CONFIG,
            DPTAM_BIGRU_CONFIG,
            ASB_DPTAM_BIGRU_CONFIG,
            BILSTM_CONFIG
        )
        print("✅ 配置文件导入成功")
        return True
    except ImportError as e:
        print(f"❌ 配置文件导入失败: {e}")
        return False

def test_cnn_bigru_config():
    """测试CNN-BiGRU配置"""
    try:
        from src.utils.config import CNN_BIGRU_CONFIG
        
        required_keys = ['name', 'cnn_filters', 'cnn_kernel_sizes', 'bigru_units', 'dense_units', 'dropout_rate', 'pool_size']
        
        for key in required_keys:
            if key not in CNN_BIGRU_CONFIG:
                print(f"❌ CNN_BIGRU_CONFIG 缺少必需的键: {key}")
                return False
        
        print("✅ CNN_BIGRU_CONFIG 配置完整")
        print(f"   模型名称: {CNN_BIGRU_CONFIG['name']}")
        print(f"   CNN滤波器: {CNN_BIGRU_CONFIG['cnn_filters']}")
        print(f"   CNN卷积核: {CNN_BIGRU_CONFIG['cnn_kernel_sizes']}")
        print(f"   BiGRU单元: {CNN_BIGRU_CONFIG['bigru_units']}")
        print(f"   全连接层: {CNN_BIGRU_CONFIG['dense_units']}")
        return True
        
    except Exception as e:
        print(f"❌ CNN_BIGRU_CONFIG 测试失败: {e}")
        return False

def test_experiment_config():
    """测试实验配置"""
    try:
        from src.utils.config import COMPARISON_EXPERIMENT_CONFIG
        
        # 检查CNN-BiGRU开关是否存在
        if 'enable_cnn_bigru' not in COMPARISON_EXPERIMENT_CONFIG:
            print("❌ enable_cnn_bigru 开关不存在于实验配置中")
            return False
        
        print("✅ CNN-BiGRU实验开关已添加")
        print(f"   enable_cnn_bigru: {COMPARISON_EXPERIMENT_CONFIG['enable_cnn_bigru']}")
        
        # 检查核心开关是否保留
        core_switches = [
            'enable_baseline_bigru',
            'enable_dptam_bigru',
            'enable_asb_dptam_bigru',
            'enable_bilstm',
            'enable_cnn_bigru'
        ]
        
        for switch in core_switches:
            if switch not in COMPARISON_EXPERIMENT_CONFIG:
                print(f"❌ 核心开关 {switch} 丢失")
                return False
        
        print("✅ 所有实验开关配置正确")
        return True
        
    except Exception as e:
        print(f"❌ 实验配置测试失败: {e}")
        return False

def test_model_import():
    """测试CNN-BiGRU模型导入"""
    try:
        from src.models.cnn_bigru_model import CNNBiGRUModel
        print("✅ CNN-BiGRU模型导入成功")
        
        # 测试模型实例化
        model = CNNBiGRUModel(
            sequence_length=24,
            n_features=25,
            cnn_filters=[32, 16],
            cnn_kernel_sizes=[3, 3],
            bigru_units=[64, 32],
            dense_units=[32, 16],
            dropout_rate=0.3,
            pool_size=2
        )
        
        print("✅ CNN-BiGRU模型实例化成功")
        
        # 获取模型信息
        model_info = model.get_model_info()
        print(f"   模型名称: {model_info['model_name']}")
        print(f"   模型类型: {model_info['model_type']}")
        print(f"   总参数量: {model_info['total_params']:,}")
        print(f"   架构: {model_info['architecture']}")
        
        return True
        
    except Exception as e:
        print(f"❌ CNN-BiGRU模型测试失败: {e}")
        return False

def test_experiment_class():
    """测试实验类导入"""
    try:
        from src.experiments.asb_dptam_advantage_comparison import ASBDPTAMAdvantageComparator
        print("✅ 实验类导入成功")
        
        # 检查是否有train_cnn_bigru方法
        comparator = ASBDPTAMAdvantageComparator()
        if not hasattr(comparator, 'train_cnn_bigru'):
            print("❌ 实验类缺少 train_cnn_bigru 方法")
            return False
        
        print("✅ train_cnn_bigru 方法存在")
        return True
        
    except Exception as e:
        print(f"❌ 实验类测试失败: {e}")
        return False

def test_model_forward():
    """测试模型前向传播"""
    try:
        import torch
        from src.models.cnn_bigru_model import CNNBiGRUModel
        
        # 创建模型
        model = CNNBiGRUModel(
            sequence_length=24,
            n_features=25,
            cnn_filters=[16, 8],  # 使用较小的配置进行测试
            cnn_kernel_sizes=[3, 3],
            bigru_units=[32, 16],
            dense_units=[16, 8],
            dropout_rate=0.3,
            pool_size=2
        )
        
        # 创建测试输入
        batch_size = 4
        test_input = torch.randn(batch_size, 24, 25)
        
        # 前向传播
        model.eval()
        with torch.no_grad():
            output = model(test_input)
        
        print("✅ 模型前向传播测试成功")
        print(f"   输入形状: {test_input.shape}")
        print(f"   输出形状: {output.shape}")
        print(f"   输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
        
        # 验证输出形状
        expected_output_shape = (batch_size, 1)
        if output.shape != expected_output_shape:
            print(f"❌ 输出形状不正确，期望 {expected_output_shape}，得到 {output.shape}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 模型前向传播测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试CNN-BiGRU模型添加结果")
    print("=" * 60)
    
    tests = [
        ("配置文件导入", test_config_import),
        ("CNN-BiGRU配置", test_cnn_bigru_config),
        ("实验配置更新", test_experiment_config),
        ("CNN-BiGRU模型导入", test_model_import),
        ("实验类更新", test_experiment_class),
        ("模型前向传播", test_model_forward)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 40)
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！CNN-BiGRU模型添加成功！")
        print("\n🎯 添加总结:")
        print("  ✨ 添加了CNN-BiGRU模型配置")
        print("  ✨ 添加了CNN-BiGRU模型文件")
        print("  ✨ 添加了CNN-BiGRU实验开关")
        print("  ✨ 添加了CNN-BiGRU训练方法")
        print("  ✨ 添加了CNN-BiGRU分析逻辑")
        print("  ✨ 保持了ASB-DPTAM核心对比实验")
        
        print("\n🔬 当前实验包含:")
        print("  • Baseline-BiGRU (基线模型)")
        print("  • DPTAM-BiGRU (时序注意力增强)")
        print("  • ASB-DPTAM-BiGRU (频域+时序双重增强)")
        print("  • BiLSTM (可选的LSTM对比)")
        print("  • CNN-BiGRU (CNN特征提取+BiGRU时序建模)")
    else:
        print("⚠️ 部分测试失败，请检查添加操作")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
