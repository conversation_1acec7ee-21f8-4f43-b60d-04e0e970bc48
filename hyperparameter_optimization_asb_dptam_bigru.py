"""
ASB-DPTAM-BiGRU模型超参数优化脚本
使用Optuna进行贝叶斯优化，寻找最佳超参数组合
"""

import os
import sys
import torch
import numpy as np
import optuna
import json
import pickle
from datetime import datetime
from typing import Dict, Any, Tuple
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.models.asb_dptam_bigru_model import ASBDPTAMBiGRUModel
from src.utils.metrics import ModelEvaluator
from src.utils.config import (
    MODEL_CONFIG, DATASET_CONFIG, setup_matplotlib,
    setup_training_session, get_current_paths
)


class ASBDPTAMBiGRUHyperparameterOptimizer:
    """ASB-DPTAM-BiGRU模型超参数优化器"""
    
    def __init__(self, n_trials: int = 100, timeout: int = None):
        """
        初始化超参数优化器
        
        Args:
            n_trials: 优化试验次数
            timeout: 优化超时时间(秒)
        """
        self.n_trials = n_trials
        self.timeout = timeout
        self.data_loader = None
        self.preprocessor = None
        self.data = None
        self.best_params = None
        self.best_score = None
        self.study = None
        
        # 设置matplotlib
        setup_matplotlib()
        
        # 设置优化会话
        setup_training_session('ASB_DPTAM_BiGRU_Hyperparameter_Optimization')
        
        print("=" * 80)
        print("ASB-DPTAM-BiGRU模型超参数优化")
        print("=" * 80)
        print(f"优化算法: Optuna (TPE)")
        print(f"试验次数: {n_trials}")
        print(f"超时时间: {timeout}秒" if timeout else "超时时间: 无限制")
        print("=" * 80)
    
    def load_and_prepare_data(self) -> Dict[str, Any]:
        """加载和预处理数据"""
        print("\n" + "=" * 60)
        print("步骤1: 数据加载与预处理")
        print("=" * 60)
        
        # 数据加载
        self.data_loader = DataLoader(
            data_file=DATASET_CONFIG['data_file']  # 使用正确的参数名
        )

        # 加载数据
        df = self.data_loader.load_data()
        print(f"数据形状: {df.shape}")

        # 数据预处理
        self.preprocessor = DataPreprocessor()
        self.preprocessor.set_data(df)
        df_processed = self.preprocessor.preprocess()
        feature_columns = self.preprocessor.select_features()

        # 准备训练数据
        processed_data = self.preprocessor.prepare_data_for_training(
            sequence_length=MODEL_CONFIG['sequence_length'],
            train_ratio=MODEL_CONFIG['train_ratio'],
            val_ratio=MODEL_CONFIG['val_ratio']
        )

        # 创建数据加载器
        train_loader, val_loader, test_loader = self.preprocessor.create_pytorch_dataloaders(
            processed_data,
            batch_size=MODEL_CONFIG['batch_size']
        )
        
        # 准备数据字典
        self.data = {
            'train_loader': train_loader,
            'val_loader': val_loader,
            'test_loader': test_loader,
            'X_train': processed_data['X_train'],
            'y_train': processed_data['y_train'],
            'X_val': processed_data['X_val'],
            'y_val': processed_data['y_val'],
            'X_test': processed_data['X_test'],
            'y_test': processed_data['y_test'],
            'sequence_length': processed_data['sequence_length'],
            'n_features': processed_data['n_features']
        }
        
        print("数据准备完成！")
        return self.data
    
    def define_hyperparameter_space(self, trial: optuna.Trial) -> Dict[str, Any]:
        """
        定义超参数搜索空间
        
        Args:
            trial: Optuna试验对象
            
        Returns:
            超参数字典
        """
        # DPTAM相关参数
        n_segment = trial.suggest_int('n_segment', 4, 12, step=2)  # 分段数: 4,6,8,10,12
        dptam_kernel_size = trial.suggest_int('dptam_kernel_size', 3, 7, step=2)  # 卷积核: 3,5,7
        
        # BiGRU相关参数
        bigru_layer1_units = trial.suggest_int('bigru_layer1_units', 32, 128, step=16)  # 第一层GRU单元数
        bigru_layer2_units = trial.suggest_int('bigru_layer2_units', 16, 64, step=8)   # 第二层GRU单元数
        
        # 全连接层参数
        dense_layer1_units = trial.suggest_int('dense_layer1_units', 16, 64, step=8)   # 第一层全连接单元数
        dense_layer2_units = trial.suggest_int('dense_layer2_units', 8, 32, step=4)    # 第二层全连接单元数
        
        # Dropout参数
        dropout_rate = trial.suggest_float('dropout_rate', 0.1, 0.5, step=0.1)  # Dropout率
        
        # ASB参数
        asb_adaptive_filter = trial.suggest_categorical('asb_adaptive_filter', [True, False])  # 自适应滤波
        
        # 训练相关参数
        learning_rate = trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True)  # 学习率
        batch_size = trial.suggest_categorical('batch_size', [16, 32, 64, 128])     # 批次大小
        
        return {
            'n_segment': n_segment,
            'dptam_kernel_size': dptam_kernel_size,
            'bigru_units': [bigru_layer1_units, bigru_layer2_units],
            'dense_units': [dense_layer1_units, dense_layer2_units],
            'dropout_rate': dropout_rate,
            'asb_adaptive_filter': asb_adaptive_filter,
            'learning_rate': learning_rate,
            'batch_size': batch_size
        }
    
    def objective_function(self, trial: optuna.Trial) -> float:
        """
        目标函数：训练模型并返回验证集性能
        
        Args:
            trial: Optuna试验对象
            
        Returns:
            验证集RMSE (越小越好)
        """
        try:
            # 获取超参数
            params = self.define_hyperparameter_space(trial)
            
            print(f"\n试验 {trial.number}: 测试参数组合")
            print(f"参数: {params}")
            
            # 检查序列长度是否能被分段数整除
            if self.data['sequence_length'] % params['n_segment'] != 0:
                print(f"序列长度{self.data['sequence_length']}不能被分段数{params['n_segment']}整除，跳过此试验")
                return float('inf')
            
            # 重新创建数据加载器（如果批次大小改变）
            if params['batch_size'] != MODEL_CONFIG['batch_size']:
                train_loader, val_loader, test_loader = self.preprocessor.create_pytorch_dataloaders(
                    {
                        'X_train': self.data['X_train'],
                        'y_train': self.data['y_train'],
                        'X_val': self.data['X_val'],
                        'y_val': self.data['y_val'],
                        'X_test': self.data['X_test'],
                        'y_test': self.data['y_test']
                    },
                    batch_size=params['batch_size']
                )
            else:
                train_loader = self.data['train_loader']
                val_loader = self.data['val_loader']
            
            # 创建模型
            model = ASBDPTAMBiGRUModel(
                sequence_length=self.data['sequence_length'],
                n_features=self.data['n_features'],
                n_segment=params['n_segment'],
                dptam_kernel_size=params['dptam_kernel_size'],
                bigru_units=params['bigru_units'],
                dense_units=params['dense_units'],
                dropout_rate=params['dropout_rate'],
                asb_adaptive_filter=params['asb_adaptive_filter']
            )
            
            # 设置标准化器
            model.set_scalers(
                self.preprocessor.scaler_X,
                self.preprocessor.scaler_y
            )
            
            # 训练模型（使用较少的epochs进行快速评估）
            history = model.train_model(
                train_loader=train_loader,
                val_loader=val_loader,
                epochs=min(50, MODEL_CONFIG['epochs']),  # 限制epochs以加速优化
                patience=10,  # 较小的patience
                learning_rate=params['learning_rate'],
                verbose=0  # 减少输出
            )
            
            # 评估模型
            results = model.evaluate(
                X_train=self.data['X_train'],
                y_train=self.data['y_train'],
                X_val=self.data['X_val'],
                y_val=self.data['y_val'],
                X_test=self.data['X_test'],
                y_test=self.data['y_test'],
                use_normalized_metrics=True,
                verbose=0
            )
            
            # 返回验证集RMSE作为优化目标
            val_rmse = results['val']['RMSE']
            print(f"验证集RMSE: {val_rmse:.4f}")
            
            # 报告中间结果给Optuna（用于剪枝）
            trial.report(val_rmse, step=0)
            
            # 检查是否应该剪枝
            if trial.should_prune():
                raise optuna.TrialPruned()
            
            return val_rmse

        except Exception as e:
            print(f"试验 {trial.number} 失败: {str(e)}")
            return float('inf')

    def run_optimization(self) -> Dict[str, Any]:
        """
        运行超参数优化

        Returns:
            优化结果字典
        """
        print("\n" + "=" * 60)
        print("步骤2: 开始超参数优化")
        print("=" * 60)

        # 创建Optuna研究
        study = optuna.create_study(
            direction='minimize',  # 最小化RMSE
            sampler=optuna.samplers.TPESampler(seed=42),  # 使用TPE采样器
            pruner=optuna.pruners.MedianPruner(  # 使用中位数剪枝器
                n_startup_trials=5,
                n_warmup_steps=10,
                interval_steps=1
            )
        )

        # 开始优化
        study.optimize(
            self.objective_function,
            n_trials=self.n_trials,
            timeout=self.timeout,
            show_progress_bar=True
        )

        # 保存研究结果
        self.study = study
        self.best_params = study.best_params
        self.best_score = study.best_value

        print(f"\n优化完成！")
        print(f"最佳验证集RMSE: {self.best_score:.4f}")
        print(f"最佳参数组合:")
        for key, value in self.best_params.items():
            print(f"  {key}: {value}")

        return {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'study': study
        }

    def save_optimization_results(self) -> None:
        """保存优化结果"""
        print("\n" + "=" * 60)
        print("步骤3: 保存优化结果")
        print("=" * 60)

        # 创建结果目录
        results_dir = os.path.join(get_current_paths()['results_root'], 'hyperparameter_optimization')
        os.makedirs(results_dir, exist_ok=True)

        # 保存最佳参数
        best_params_file = os.path.join(results_dir, 'best_hyperparameters.json')
        with open(best_params_file, 'w', encoding='utf-8') as f:
            json.dump({
                'best_params': self.best_params,
                'best_score': self.best_score,
                'optimization_time': datetime.now().isoformat(),
                'n_trials': self.n_trials
            }, f, indent=2, ensure_ascii=False)

        # 保存完整的研究对象
        study_file = os.path.join(results_dir, 'optuna_study.pkl')
        with open(study_file, 'wb') as f:
            pickle.dump(self.study, f)

        print(f"最佳参数已保存到: {best_params_file}")
        print(f"完整研究对象已保存到: {study_file}")

    def visualize_optimization_results(self) -> None:
        """可视化优化结果"""
        print("\n" + "=" * 60)
        print("步骤4: 可视化优化结果")
        print("=" * 60)

        # 创建可视化目录
        viz_dir = os.path.join(get_current_paths()['results_root'], 'hyperparameter_optimization', 'visualizations')
        os.makedirs(viz_dir, exist_ok=True)

        # 1. 优化历史
        fig, ax = plt.subplots(figsize=(12, 6))
        optuna.visualization.matplotlib.plot_optimization_history(self.study, ax=ax)
        plt.title('超参数优化历史', fontsize=14, fontweight='bold')
        plt.xlabel('试验次数')
        plt.ylabel('验证集RMSE')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(viz_dir, 'optimization_history.png'), dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 参数重要性
        try:
            fig, ax = plt.subplots(figsize=(10, 8))
            optuna.visualization.matplotlib.plot_param_importances(self.study, ax=ax)
            plt.title('超参数重要性分析', fontsize=14, fontweight='bold')
            plt.tight_layout()
            plt.savefig(os.path.join(viz_dir, 'param_importances.png'), dpi=300, bbox_inches='tight')
            plt.close()
        except Exception as e:
            print(f"参数重要性图生成失败: {e}")

        # 3. 并行坐标图
        try:
            fig, ax = plt.subplots(figsize=(14, 8))
            optuna.visualization.matplotlib.plot_parallel_coordinate(self.study, ax=ax)
            plt.title('超参数并行坐标图', fontsize=14, fontweight='bold')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(os.path.join(viz_dir, 'parallel_coordinate.png'), dpi=300, bbox_inches='tight')
            plt.close()
        except Exception as e:
            print(f"并行坐标图生成失败: {e}")

        # 4. 试验分布热图
        try:
            # 获取试验数据
            trials_df = self.study.trials_dataframe()

            # 选择数值型参数进行相关性分析
            numeric_params = ['n_segment', 'dptam_kernel_size', 'bigru_layer1_units',
                            'bigru_layer2_units', 'dense_layer1_units', 'dense_layer2_units',
                            'dropout_rate', 'learning_rate']

            param_cols = [f'params_{param}' for param in numeric_params if f'params_{param}' in trials_df.columns]

            if len(param_cols) > 1:
                fig, ax = plt.subplots(figsize=(12, 10))
                correlation_data = trials_df[param_cols + ['value']].corr()
                sns.heatmap(correlation_data, annot=True, cmap='coolwarm', center=0, ax=ax)
                plt.title('超参数与目标值相关性热图', fontsize=14, fontweight='bold')
                plt.tight_layout()
                plt.savefig(os.path.join(viz_dir, 'correlation_heatmap.png'), dpi=300, bbox_inches='tight')
                plt.close()
        except Exception as e:
            print(f"相关性热图生成失败: {e}")

        print(f"可视化结果已保存到: {viz_dir}")

    def train_best_model(self) -> Dict[str, Any]:
        """使用最佳参数训练完整模型"""
        print("\n" + "=" * 60)
        print("步骤5: 使用最佳参数训练完整模型")
        print("=" * 60)

        # 重新创建数据加载器（使用最佳批次大小）
        train_loader, val_loader, test_loader = self.preprocessor.create_pytorch_dataloaders(
            {
                'X_train': self.data['X_train'],
                'y_train': self.data['y_train'],
                'X_val': self.data['X_val'],
                'y_val': self.data['y_val'],
                'X_test': self.data['X_test'],
                'y_test': self.data['y_test']
            },
            batch_size=self.best_params['batch_size']
        )

        # 创建最佳模型
        best_model = ASBDPTAMBiGRUModel(
            sequence_length=self.data['sequence_length'],
            n_features=self.data['n_features'],
            n_segment=self.best_params['n_segment'],
            dptam_kernel_size=self.best_params['dptam_kernel_size'],
            bigru_units=self.best_params['bigru_units'],
            dense_units=self.best_params['dense_units'],
            dropout_rate=self.best_params['dropout_rate'],
            asb_adaptive_filter=self.best_params['asb_adaptive_filter']
        )

        # 设置标准化器
        best_model.set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )

        # 训练模型（使用完整epochs）
        print("开始训练最佳模型...")
        history = best_model.train_model(
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=self.best_params['learning_rate']
        )

        # 评估最佳模型
        results = best_model.evaluate(
            X_train=self.data['X_train'],
            y_train=self.data['y_train'],
            X_val=self.data['X_val'],
            y_val=self.data['y_val'],
            X_test=self.data['X_test'],
            y_test=self.data['y_test'],
            use_normalized_metrics=True
        )

        # 保存最佳模型
        model_dir = os.path.join(get_current_paths()['results_root'], 'hyperparameter_optimization')
        best_model.save_model(save_dir=model_dir, model_name='best_asb_dptam_bigru_model')

        print("最佳模型训练完成并已保存！")

        return {
            'model': best_model,
            'history': history,
            'results': results
        }

    def run_complete_optimization(self) -> Dict[str, Any]:
        """运行完整的超参数优化流程"""
        try:
            # 步骤1: 数据准备
            self.load_and_prepare_data()

            # 步骤2: 超参数优化
            optimization_results = self.run_optimization()

            # 步骤3: 保存结果
            self.save_optimization_results()

            # 步骤4: 可视化
            self.visualize_optimization_results()

            # 步骤5: 训练最佳模型
            best_model_results = self.train_best_model()

            print(f"\n✅ 超参数优化完成！")
            print(f"📁 结果保存路径: {get_current_paths()['results_root']}/hyperparameter_optimization")
            print(f"🏆 最佳验证集RMSE: {self.best_score:.4f}")

            return {
                'optimization_results': optimization_results,
                'best_model_results': best_model_results,
                'best_params': self.best_params,
                'best_score': self.best_score
            }

        except Exception as e:
            print(f"\n❌ 超参数优化过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """主函数"""
    # 创建超参数优化器
    optimizer = ASBDPTAMBiGRUHyperparameterOptimizer(
        n_trials=100,  # 可以根据计算资源调整
        timeout=None   # 可以设置超时时间，如 3600 (1小时)
    )

    # 运行完整优化流程
    results = optimizer.run_complete_optimization()

    # 打印最终结果摘要
    print("\n" + "=" * 80)
    print("优化结果摘要")
    print("=" * 80)
    print(f"最佳验证集RMSE: {results['best_score']:.4f}")
    print("\n最佳超参数:")
    for key, value in results['best_params'].items():
        print(f"  {key}: {value}")
    print("=" * 80)


if __name__ == "__main__":
    main()
