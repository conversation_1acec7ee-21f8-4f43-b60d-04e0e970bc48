importpandasaspdimportnumpyasnpimportmatplotlib.pyplotaspltimportseabornassnsfromsklearn.preprocessingimportStandardScaler,MinMaxScalerimportwarningswarnings.filterwarnings('ignore')defload_and_analyze_data(file_path):"""加载并分析风电数据集"""print("正在加载数据...")df=pd.read_csv(file_path)#基本信息print(f"数据形状:{df.shape}")print(f"数据列:{df.columns.tolist()}")print("\n数据类型:")print(df.dtypes)#转换日期列df['date']=pd.to_datetime(df['date'])#检查缺失值print("\n缺失值统计:")missing_values=df.isnull().sum()print(missing_values[missing_values>0])#基本统计信息print("\n数据统计信息:")print(df.describe())returndfdefvisualize_data(df):"""数据可视化分析"""plt.figure(figsize=(15,12))#功率时间序列图plt.subplot(3,2,1)plt.plot(df['date'][:1000],df['Power'][:1000])#显示前1000个点plt.title('风电功率时间序列(前1000个数据点)')plt.xlabel('时间')plt.ylabel('功率')plt.xticks(rotation=45)#功率分布直方图plt.subplot(3,2,2)plt.hist(df['Power'],bins=50,alpha=0.7)plt.title('风电功率分布')plt.xlabel('功率')plt.ylabel('频次')#风速与功率的关系plt.subplot(3,2,3)plt.scatter(df['Wind_speed_hub'][:5000],df['Power'][:5000],alpha=0.5)plt.title('轮毂高度风速vs功率')plt.xlabel('轮毂高度风速')plt.ylabel('功率')#温度与功率的关系plt.subplot(3,2,4)plt.scatter(df['Air_temperature'][:5000],df['Power'][:5000],alpha=0.5)plt.title('气温vs功率')plt.xlabel('气温')plt.ylabel('功率')#相关性热力图plt.subplot(3,2,5)#选择数值列进行相关性分析numeric_cols=df.select_dtypes(include=[np.number]).columnscorrelation_matrix=df[numeric_cols].corr()sns.heatmap(correlation_matrix,annot=False,cmap='coolwarm',center=0)plt.title('特征相关性热力图')#功率的月度变化plt.subplot(3,2,6)df['month']=df['date'].dt.monthmonthly_power=df.groupby('month')['Power'].mean()plt.plot(monthly_power.index,monthly_power.values,marker='o')plt.title('月度平均功率变化')plt.xlabel('月份')plt.ylabel('平均功率')plt.tight_layout()plt.savefig('data_analysis.png',dpi=300,bbox_inches='tight')plt.show()defpreprocess_data(df):"""数据预处理"""print("开始数据预处理...")#创建副本df_processed=df.copy()#处理缺失值（如果有）ifdf_processed.isnull().sum().sum()>0:print("处理缺失值...")#对数值列使用前向填充numeric_cols=df_processed.select_dtypes(include=[np.number]).columnsdf_processed[numeric_cols]=df_processed[numeric_cols].fillna(method='ffill')df_processed[numeric_cols]=df_processed[numeric_cols].fillna(method='bfill')#特征工程print("进行特征工程...")#时间特征df_processed['hour']=df_processed['date'].dt.hourdf_processed['day_of_week']=df_processed['date'].dt.dayofweekdf_processed['month']=df_processed['date'].dt.monthdf_processed['day_of_year']=df_processed['date'].dt.dayofyear#周期性特征编码df_processed['hour_sin']=np.sin(2*np.pi*df_processed['hour']/24)df_processed['hour_cos']=np.cos(2*np.pi*df_processed['hour']/24)df_processed['day_sin']=np.sin(2*np.pi*df_processed['day_of_week']/7)df_processed['day_cos']=np.cos(2*np.pi*df_processed['day_of_week']/7)df_processed['month_sin']=np.sin(2*np.pi*df_processed['month']/12)df_processed['month_cos']=np.cos(2*np.pi*df_processed['month']/12)#风速相关特征df_processed['wind_speed_diff']=df_processed['Wind_speed_hub']-df_processed['Wind_speed_10m']df_processed['wind_direction_diff']=df_processed['Wind_direction_hub']-df_processed['Wind_direction_10m']#滞后特征（前一个时间点的功率）df_processed['Power_lag1']=df_processed['Power'].shift(1)df_processed['Power_lag2']=df_processed['Power'].shift(2)df_processed['Power_lag3']=df_processed['Power'].shift(3)#滑动平均特征df_processed['Power_ma3']=df_processed['Power'].rolling(window=3).mean()df_processed['Power_ma6']=df_processed['Power'].rolling(window=6).mean()df_processed['Wind_speed_ma3']=df_processed['Wind_speed_hub'].rolling(window=3).mean()#删除包含NaN的行（由于滞后和滑动平均产生的）df_processed=df_processed.dropna()print(f"预处理后数据形状:{df_processed.shape}")returndf_processeddefselect_features(df_processed):"""选择用于模型训练的特征"""#选择特征列（排除日期和目标变量）feature_columns=['Wind_speed_10m','Wind_direction_10m','Wind_speed_30m','Wind_direction_30m','Wind_speed_50m','Wind_direction_50m','Wind_speed_hub','Wind_direction_hub','Air_temperature','Atmosphere','Relative_humidity','hour_sin','hour_cos','day_sin','day_cos','month_sin','month_cos','wind_speed_diff','wind_direction_diff','Power_lag1','Power_lag2','Power_lag3','Power_ma3','Power_ma6','Wind_speed_ma3']#确保所有特征列都存在available_features=[colforcolinfeature_columnsifcolindf_processed.columns]print(f"选择的特征数量:{len(available_features)}")print(f"特征列表:{available_features}")returnavailable_featuresif__name__=="__main__":#加载和分析数据df=load_and_analyze_data('Site_1_standardized.csv')#数据可视化visualize_data(df)#数据预处理df_processed=preprocess_data(df)#特征选择feature_columns=select_features(df_processed)#保存预处理后的数据df_processed.to_csv('processed_data.csv',index=False)print("预处理完成，数据已保存到processed_data.csv")#保存特征列表withopen('feature_columns.txt','w')asf:forfeatureinfeature_columns:f.write(f"{feature}\n")print("特征列表已保存到feature_columns.txt")