#!/usr/bin/env python3
"""
测试CNN模型删除后的配置验证
"""

import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_import():
    """测试配置文件导入"""
    try:
        from src.utils.config import (
            COMPARISON_EXPERIMENT_CONFIG,
            BIGRU_CONFIG,
            DPTAM_BIGRU_CONFIG,
            ASB_DPTAM_BIGRU_CONFIG,
            BILSTM_CONFIG
        )
        print("✅ 配置文件导入成功")
        return True
    except ImportError as e:
        print(f"❌ 配置文件导入失败: {e}")
        return False

def test_cnn_configs_removed():
    """测试CNN配置是否已删除"""
    try:
        from src.utils.config import CNN_BILSTM_CONFIG
        print("❌ CNN_BILSTM_CONFIG 仍然存在，删除失败")
        return False
    except ImportError:
        print("✅ CNN_BILSTM_CONFIG 已成功删除")
    
    try:
        from src.utils.config import CNN_BILSTM_ATTENTION_CONFIG
        print("❌ CNN_BILSTM_ATTENTION_CONFIG 仍然存在，删除失败")
        return False
    except ImportError:
        print("✅ CNN_BILSTM_ATTENTION_CONFIG 已成功删除")
    
    try:
        from src.utils.config import CNN_BIGRU_CONFIG
        print("❌ CNN_BIGRU_CONFIG 仍然存在，删除失败")
        return False
    except ImportError:
        print("✅ CNN_BIGRU_CONFIG 已成功删除")
    
    try:
        from src.utils.config import CNN_BIGRU_ATTENTION_CONFIG
        print("❌ CNN_BIGRU_ATTENTION_CONFIG 仍然存在，删除失败")
        return False
    except ImportError:
        print("✅ CNN_BIGRU_ATTENTION_CONFIG 已成功删除")
    
    return True

def test_experiment_config():
    """测试实验配置"""
    try:
        from src.utils.config import COMPARISON_EXPERIMENT_CONFIG
        
        # 检查CNN相关开关是否已删除
        cnn_switches = [
            'enable_cnn_bilstm',
            'enable_cnn_bilstm_attention', 
            'enable_cnn_bigru',
            'enable_cnn_bigru_attention'
        ]
        
        for switch in cnn_switches:
            if switch in COMPARISON_EXPERIMENT_CONFIG:
                print(f"❌ {switch} 仍然存在于配置中")
                return False
        
        print("✅ 所有CNN相关开关已从实验配置中删除")
        
        # 检查核心开关是否保留
        core_switches = [
            'enable_baseline_bigru',
            'enable_dptam_bigru',
            'enable_asb_dptam_bigru',
            'enable_bilstm'
        ]
        
        for switch in core_switches:
            if switch not in COMPARISON_EXPERIMENT_CONFIG:
                print(f"❌ 核心开关 {switch} 意外丢失")
                return False
        
        print("✅ 所有核心模型开关保留完整")
        return True
        
    except Exception as e:
        print(f"❌ 实验配置测试失败: {e}")
        return False

def test_model_imports():
    """测试模型导入"""
    try:
        from src.models.bigru_model import BiGRUModel
        from src.models.dptam_bigru_model import DPTAMBiGRUModel
        from src.models.asb_dptam_bigru_model import ASBDPTAMBiGRUModel
        from src.models.bilstm_model import BiLSTMModel
        print("✅ 核心模型导入成功")
    except ImportError as e:
        print(f"❌ 核心模型导入失败: {e}")
        return False

    # 测试CNN模型文件是否已删除
    import os
    cnn_model_files = [
        'src/models/cnn_bilstm_model.py',
        'src/models/cnn_bilstm_attention_model.py',
        'src/models/cnn_bigru_model.py',
        'src/models/cnn_bigru_attention_model.py'
    ]

    for model_file in cnn_model_files:
        if os.path.exists(model_file):
            print(f"❌ {model_file} 文件仍然存在，删除失败")
            return False
        else:
            print(f"✅ {model_file} 文件已成功删除")

    return True

def test_experiment_class():
    """测试实验类是否可以正常实例化"""
    try:
        from src.experiments.asb_dptam_advantage_comparison import ASBDPTAMAdvantageComparator
        
        # 尝试实例化（不运行实验）
        print("🔍 测试实验类实例化...")
        # 这里只是导入测试，不实际运行
        print("✅ ASBDPTAMAdvantageComparator 类导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 实验类测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试CNN模型删除结果")
    print("=" * 60)
    
    tests = [
        ("配置文件导入", test_config_import),
        ("CNN配置删除", test_cnn_configs_removed),
        ("实验配置更新", test_experiment_config),
        ("模型文件删除", test_model_imports),
        ("实验类导入", test_experiment_class)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("-" * 40)
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！CNN模型删除成功！")
        print("\n🎯 删除总结:")
        print("  ✨ 删除了4个CNN模型配置")
        print("  ✨ 删除了4个CNN模型文件")
        print("  ✨ 删除了4个CNN实验开关")
        print("  ✨ 删除了所有CNN相关训练方法")
        print("  ✨ 删除了所有CNN相关分析逻辑")
        print("  ✨ 保留了ASB-DPTAM核心对比实验")
        
        print("\n🔬 当前实验专注于:")
        print("  • Baseline-BiGRU (基线模型)")
        print("  • DPTAM-BiGRU (时序注意力增强)")
        print("  • ASB-DPTAM-BiGRU (频域+时序双重增强)")
        print("  • BiLSTM (可选的LSTM对比)")
    else:
        print("⚠️ 部分测试失败，请检查删除操作")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
